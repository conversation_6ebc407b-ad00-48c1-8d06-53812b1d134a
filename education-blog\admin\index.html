<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blog Admin Panel | Little Star Education</title>
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Admin Styles -->
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link rel="stylesheet" href="admin-styles.css">
    
    <!-- Rich Text Editor -->
    <script src="https://cdn.tiny.cloud/1/no-api-key/tinymce/6/tinymce.min.js" referrerpolicy="origin"></script>
</head>
<body class="admin-body">
    <!-- Admin Header -->
    <header class="admin-header">
        <div class="admin-container">
            <div class="admin-header-content">
                <h1>📝 Blog Admin Panel</h1>
                <nav class="admin-nav">
                    <a href="#dashboard" class="admin-nav-link active" data-section="dashboard">Dashboard</a>
                    <a href="#posts" class="admin-nav-link" data-section="posts">Posts</a>
                    <a href="#categories" class="admin-nav-link" data-section="categories">Categories</a>
                    <a href="#seo" class="admin-nav-link" data-section="seo">SEO Settings</a>
                    <a href="#analytics" class="admin-nav-link" data-section="analytics">Analytics</a>
                    <a href="../" class="admin-nav-link view-site">View Site</a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Admin Main Content -->
    <main class="admin-main">
        <div class="admin-container">
            <!-- Dashboard Section -->
            <section id="dashboard-section" class="admin-section active">
                <div class="admin-section-header">
                    <h2>📊 Dashboard</h2>
                    <p>Overview of your blog performance and quick actions</p>
                </div>

                <!-- Stats Cards -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">📄</div>
                        <div class="stat-content">
                            <h3 id="total-posts">6</h3>
                            <p>Total Posts</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">👀</div>
                        <div class="stat-content">
                            <h3 id="total-views">5,590</h3>
                            <p>Total Views</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">📈</div>
                        <div class="stat-content">
                            <h3 id="monthly-growth">+23%</h3>
                            <p>Monthly Growth</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">🔗</div>
                        <div class="stat-content">
                            <h3 id="main-site-clicks">342</h3>
                            <p>Main Site Clicks</p>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="quick-actions">
                    <h3>Quick Actions</h3>
                    <div class="action-buttons">
                        <button class="btn btn-primary" onclick="showNewPostForm()">
                            ✏️ New Post
                        </button>
                        <button class="btn btn-secondary" onclick="generateSitemap()">
                            🗺️ Generate Sitemap
                        </button>
                        <button class="btn btn-secondary" onclick="optimizeImages()">
                            🖼️ Optimize Images
                        </button>
                        <button class="btn btn-secondary" onclick="checkSEO()">
                            🔍 SEO Check
                        </button>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="recent-activity">
                    <h3>Recent Activity</h3>
                    <div class="activity-list" id="activity-list">
                        <!-- Activity items will be loaded here -->
                    </div>
                </div>
            </section>

            <!-- Posts Section -->
            <section id="posts-section" class="admin-section">
                <div class="admin-section-header">
                    <h2>📄 Manage Posts</h2>
                    <button class="btn btn-primary" onclick="showNewPostForm()">
                        ✏️ New Post
                    </button>
                </div>

                <!-- Posts Filter -->
                <div class="posts-filter">
                    <input type="search" id="posts-search" placeholder="Search posts..." class="search-input">
                    <select id="category-filter" class="filter-select">
                        <option value="">All Categories</option>
                        <option value="teaching-resources">Teaching Resources</option>
                        <option value="worksheet-ideas">Worksheet Ideas</option>
                        <option value="classroom-management">Classroom Management</option>
                        <option value="educational-technology">Educational Technology</option>
                        <option value="parent-tips">Parent Tips</option>
                        <option value="learning-activities">Learning Activities</option>
                    </select>
                    <select id="status-filter" class="filter-select">
                        <option value="">All Status</option>
                        <option value="published">Published</option>
                        <option value="draft">Draft</option>
                        <option value="scheduled">Scheduled</option>
                    </select>
                </div>

                <!-- Posts Table -->
                <div class="posts-table-container">
                    <table class="posts-table">
                        <thead>
                            <tr>
                                <th>Title</th>
                                <th>Category</th>
                                <th>Author</th>
                                <th>Date</th>
                                <th>Status</th>
                                <th>Views</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="posts-table-body">
                            <!-- Posts will be loaded here -->
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- Categories Section -->
            <section id="categories-section" class="admin-section">
                <div class="admin-section-header">
                    <h2>🏷️ Manage Categories</h2>
                    <button class="btn btn-primary" onclick="showNewCategoryForm()">
                        ➕ New Category
                    </button>
                </div>

                <div class="categories-grid" id="categories-grid">
                    <!-- Categories will be loaded here -->
                </div>
            </section>

            <!-- SEO Section -->
            <section id="seo-section" class="admin-section">
                <div class="admin-section-header">
                    <h2>🔍 SEO Settings</h2>
                    <p>Optimize your blog for search engines</p>
                </div>

                <div class="seo-settings">
                    <!-- Global SEO Settings -->
                    <div class="seo-card">
                        <h3>Global SEO Settings</h3>
                        <form id="global-seo-form">
                            <div class="form-group">
                                <label for="site-title">Site Title</label>
                                <input type="text" id="site-title" value="Educational Resources Blog | Little Star Education" maxlength="60">
                                <small>Recommended: 50-60 characters</small>
                            </div>
                            <div class="form-group">
                                <label for="site-description">Meta Description</label>
                                <textarea id="site-description" maxlength="160">Discover free educational resources, teaching tips, printable worksheets, and classroom activities. Expert advice for teachers, parents, and educators.</textarea>
                                <small>Recommended: 150-160 characters</small>
                            </div>
                            <div class="form-group">
                                <label for="site-keywords">Keywords</label>
                                <input type="text" id="site-keywords" value="educational resources, teaching tips, free worksheets, classroom activities">
                            </div>
                            <button type="submit" class="btn btn-primary">Save SEO Settings</button>
                        </form>
                    </div>

                    <!-- SEO Tools -->
                    <div class="seo-card">
                        <h3>SEO Tools</h3>
                        <div class="seo-tools">
                            <button class="btn btn-secondary" onclick="generateSitemap()">
                                🗺️ Generate XML Sitemap
                            </button>
                            <button class="btn btn-secondary" onclick="generateRobotsTxt()">
                                🤖 Generate Robots.txt
                            </button>
                            <button class="btn btn-secondary" onclick="checkBrokenLinks()">
                                🔗 Check Broken Links
                            </button>
                            <button class="btn btn-secondary" onclick="analyzePageSpeed()">
                                ⚡ Analyze Page Speed
                            </button>
                        </div>
                    </div>

                    <!-- SEO Checklist -->
                    <div class="seo-card">
                        <h3>SEO Checklist</h3>
                        <div class="seo-checklist" id="seo-checklist">
                            <!-- Checklist items will be loaded here -->
                        </div>
                    </div>
                </div>
            </section>

            <!-- Analytics Section -->
            <section id="analytics-section" class="admin-section">
                <div class="admin-section-header">
                    <h2>📈 Analytics</h2>
                    <p>Track your blog performance and traffic</p>
                </div>

                <!-- Analytics Cards -->
                <div class="analytics-grid">
                    <div class="analytics-card">
                        <h3>Traffic Overview</h3>
                        <div class="analytics-chart" id="traffic-chart">
                            <!-- Chart will be rendered here -->
                        </div>
                    </div>
                    <div class="analytics-card">
                        <h3>Top Posts</h3>
                        <div class="top-posts-list" id="top-posts-list">
                            <!-- Top posts will be loaded here -->
                        </div>
                    </div>
                    <div class="analytics-card">
                        <h3>Traffic Sources</h3>
                        <div class="traffic-sources" id="traffic-sources">
                            <!-- Traffic sources will be loaded here -->
                        </div>
                    </div>
                    <div class="analytics-card">
                        <h3>Main Site Conversions</h3>
                        <div class="conversions-data" id="conversions-data">
                            <!-- Conversion data will be loaded here -->
                        </div>
                    </div>
                </div>

                <!-- Google Analytics Integration -->
                <div class="analytics-integration">
                    <h3>Google Analytics Integration</h3>
                    <form id="analytics-form">
                        <div class="form-group">
                            <label for="ga-tracking-id">Google Analytics Tracking ID</label>
                            <input type="text" id="ga-tracking-id" placeholder="GA_MEASUREMENT_ID">
                        </div>
                        <div class="form-group">
                            <label for="gsc-verification">Search Console Verification</label>
                            <input type="text" id="gsc-verification" placeholder="google-site-verification code">
                        </div>
                        <button type="submit" class="btn btn-primary">Save Analytics Settings</button>
                    </form>
                </div>
            </section>
        </div>
    </main>

    <!-- New Post Modal -->
    <div id="new-post-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>✏️ Create New Post</h3>
                <button class="modal-close" onclick="closeModal('new-post-modal')">&times;</button>
            </div>
            <form id="new-post-form" class="modal-body">
                <div class="form-row">
                    <div class="form-group">
                        <label for="post-title">Post Title *</label>
                        <input type="text" id="post-title" required maxlength="100">
                        <small>SEO Title Length: <span id="title-length">0</span>/60 characters</small>
                    </div>
                    <div class="form-group">
                        <label for="post-slug">URL Slug</label>
                        <input type="text" id="post-slug">
                        <small>Auto-generated from title</small>
                    </div>
                </div>

                <div class="form-group">
                    <label for="post-excerpt">Excerpt *</label>
                    <textarea id="post-excerpt" required maxlength="160" rows="3"></textarea>
                    <small>Meta Description Length: <span id="excerpt-length">0</span>/160 characters</small>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="post-category">Category *</label>
                        <select id="post-category" required>
                            <option value="">Select Category</option>
                            <option value="teaching-resources">Teaching Resources</option>
                            <option value="worksheet-ideas">Worksheet Ideas</option>
                            <option value="classroom-management">Classroom Management</option>
                            <option value="educational-technology">Educational Technology</option>
                            <option value="parent-tips">Parent Tips</option>
                            <option value="learning-activities">Learning Activities</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="post-author">Author</label>
                        <input type="text" id="post-author" value="Admin">
                    </div>
                </div>

                <div class="form-group">
                    <label for="post-keywords">SEO Keywords</label>
                    <input type="text" id="post-keywords" placeholder="keyword1, keyword2, keyword3">
                    <small>Separate keywords with commas</small>
                </div>

                <div class="form-group">
                    <label for="post-image">Featured Image URL</label>
                    <input type="url" id="post-image" placeholder="https://example.com/image.jpg">
                </div>

                <div class="form-group">
                    <label for="post-content">Content *</label>
                    <textarea id="post-content" required></textarea>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="post-featured"> Featured Post
                        </label>
                    </div>
                    <div class="form-group">
                        <label for="post-status">Status</label>
                        <select id="post-status">
                            <option value="draft">Draft</option>
                            <option value="published">Published</option>
                            <option value="scheduled">Scheduled</option>
                        </select>
                    </div>
                </div>

                <!-- SEO Preview -->
                <div class="seo-preview">
                    <h4>SEO Preview</h4>
                    <div class="seo-preview-content">
                        <div class="seo-title" id="seo-preview-title">Your post title will appear here</div>
                        <div class="seo-url" id="seo-preview-url">https://blog.littlestareducation.com/post/your-slug/</div>
                        <div class="seo-description" id="seo-preview-description">Your post excerpt will appear here</div>
                    </div>
                </div>

                <!-- Internal Linking Suggestions -->
                <div class="internal-linking">
                    <h4>Internal Linking Suggestions</h4>
                    <div id="linking-suggestions">
                        <!-- Suggestions will be loaded here -->
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('new-post-modal')">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create Post</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../assets/js/main.js"></script>
    <script src="admin.js"></script>
</body>
</html>
