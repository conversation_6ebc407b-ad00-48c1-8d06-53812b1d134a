// Blog Management System
class BlogManager {
    constructor() {
        this.posts = [];
        this.categories = [
            { id: 'teaching-resources', name: 'Teaching Resources', slug: 'teaching-resources' },
            { id: 'worksheet-ideas', name: 'Worksheet Ideas', slug: 'worksheet-ideas' },
            { id: 'classroom-management', name: 'Classroom Management', slug: 'classroom-management' },
            { id: 'educational-technology', name: 'Educational Technology', slug: 'educational-technology' },
            { id: 'parent-tips', name: 'Parent Tips', slug: 'parent-tips' },
            { id: 'learning-activities', name: 'Learning Activities', slug: 'learning-activities' }
        ];
        this.currentPage = 1;
        this.postsPerPage = 6;
        this.init();
    }

    init() {
        this.loadSamplePosts();
        this.setupEventListeners();
        this.renderFeaturedPosts();
        this.renderLatestPosts();
        this.renderPopularPosts();
        this.setupMobileMenu();
        this.setupSearch();
        this.setupNewsletter();
    }

    // Load sample educational blog posts
    loadSamplePosts() {
        this.posts = [
            {
                id: 1,
                title: "10 Free Printable Math Worksheets for Grade 3 Students",
                slug: "free-printable-math-worksheets-grade-3",
                excerpt: "Discover our collection of engaging math worksheets designed specifically for third-grade students. These free printables cover addition, subtraction, multiplication, and problem-solving skills.",
                content: "Full article content here...",
                category: "worksheet-ideas",
                author: "Sarah Johnson",
                date: "2025-01-15",
                image: "assets/images/math-worksheets-grade3.jpg",
                featured: true,
                views: 1250,
                metaTitle: "10 Free Printable Math Worksheets for Grade 3 | Little Star Education",
                metaDescription: "Download free Grade 3 math worksheets covering addition, subtraction, multiplication. Perfect for classroom or home learning. Printable PDF format.",
                keywords: ["grade 3 math worksheets", "free printable worksheets", "elementary math", "third grade math"],
                readTime: "5 min read"
            },
            {
                id: 2,
                title: "How to Create Engaging Reading Comprehension Activities",
                slug: "create-engaging-reading-comprehension-activities",
                excerpt: "Learn proven strategies to develop reading comprehension activities that capture students' attention and improve their understanding of texts.",
                content: "Full article content here...",
                category: "teaching-resources",
                author: "Michael Chen",
                date: "2025-01-12",
                image: "assets/images/reading-comprehension.jpg",
                featured: true,
                views: 980,
                metaTitle: "How to Create Engaging Reading Comprehension Activities | Teaching Tips",
                metaDescription: "Discover effective strategies for creating reading comprehension activities that engage students and improve text understanding. Expert teaching tips included.",
                keywords: ["reading comprehension", "teaching strategies", "literacy activities", "classroom activities"],
                readTime: "7 min read"
            },
            {
                id: 3,
                title: "Best Educational Apps for Remote Learning in 2025",
                slug: "best-educational-apps-remote-learning-2025",
                excerpt: "Explore the top educational apps that make remote learning effective and engaging for students of all ages. Includes free and premium options.",
                content: "Full article content here...",
                category: "educational-technology",
                author: "Lisa Rodriguez",
                date: "2025-01-10",
                image: "assets/images/educational-apps.jpg",
                featured: false,
                views: 750,
                metaTitle: "Best Educational Apps for Remote Learning 2025 | EdTech Guide",
                metaDescription: "Discover the top educational apps for remote learning. Free and premium options for all grade levels. Perfect for distance learning and homeschooling.",
                keywords: ["educational apps", "remote learning", "distance learning", "educational technology"],
                readTime: "6 min read"
            },
            {
                id: 4,
                title: "Classroom Organization Tips for New Teachers",
                slug: "classroom-organization-tips-new-teachers",
                excerpt: "Essential classroom organization strategies that will help new teachers create an efficient and productive learning environment from day one.",
                content: "Full article content here...",
                category: "classroom-management",
                author: "David Thompson",
                date: "2025-01-08",
                image: "assets/images/classroom-organization.jpg",
                featured: false,
                views: 620,
                metaTitle: "Classroom Organization Tips for New Teachers | Teaching Guide",
                metaDescription: "Essential classroom organization tips for new teachers. Create an efficient learning environment with these proven strategies and practical advice.",
                keywords: ["classroom organization", "new teachers", "teaching tips", "classroom management"],
                readTime: "8 min read"
            },
            {
                id: 5,
                title: "Fun Science Experiments for Elementary Students",
                slug: "fun-science-experiments-elementary-students",
                excerpt: "Engage young learners with these safe, easy-to-conduct science experiments that demonstrate key scientific concepts in an exciting way.",
                content: "Full article content here...",
                category: "learning-activities",
                author: "Emma Wilson",
                date: "2025-01-05",
                image: "assets/images/science-experiments.jpg",
                featured: true,
                views: 1100,
                metaTitle: "Fun Science Experiments for Elementary Students | STEM Activities",
                metaDescription: "Discover safe and engaging science experiments for elementary students. Easy-to-follow instructions for classroom or home learning activities.",
                keywords: ["science experiments", "elementary science", "STEM activities", "hands-on learning"],
                readTime: "10 min read"
            },
            {
                id: 6,
                title: "Parent Guide: Supporting Your Child's Math Learning at Home",
                slug: "parent-guide-supporting-child-math-learning-home",
                excerpt: "Practical tips for parents to help their children succeed in mathematics, including fun activities and resources for home practice.",
                content: "Full article content here...",
                category: "parent-tips",
                author: "Jennifer Lee",
                date: "2025-01-03",
                image: "assets/images/parent-math-support.jpg",
                featured: false,
                views: 890,
                metaTitle: "Parent Guide: Supporting Your Child's Math Learning at Home",
                metaDescription: "Help your child succeed in math with these practical tips and activities for home learning. Expert advice for parents from education specialists.",
                keywords: ["parent tips", "math learning", "home learning", "elementary math"],
                readTime: "6 min read"
            }
        ];
    }

    // Setup event listeners
    setupEventListeners() {
        // Mobile menu toggle
        const mobileToggle = document.querySelector('.mobile-menu-toggle');
        const navMenu = document.querySelector('.nav-menu');
        
        if (mobileToggle && navMenu) {
            mobileToggle.addEventListener('click', () => {
                navMenu.classList.toggle('active');
            });
        }

        // Search form
        const searchForm = document.querySelector('.search-form');
        if (searchForm) {
            searchForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleSearch();
            });
        }

        // Newsletter form
        const newsletterForm = document.querySelector('.newsletter-form');
        if (newsletterForm) {
            newsletterForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleNewsletterSignup();
            });
        }
    }

    // Render featured posts
    renderFeaturedPosts() {
        const container = document.getElementById('featured-posts');
        if (!container) return;

        const featuredPosts = this.posts.filter(post => post.featured).slice(0, 3);
        
        container.innerHTML = featuredPosts.map(post => `
            <article class="post-card" itemscope itemtype="https://schema.org/BlogPosting">
                <img src="${post.image}" alt="${post.title}" class="post-image" itemprop="image">
                <div class="post-content">
                    <div class="post-meta">
                        <span itemprop="author">${post.author}</span>
                        <span>•</span>
                        <time itemprop="datePublished" datetime="${post.date}">${this.formatDate(post.date)}</time>
                        <span>•</span>
                        <span>${post.readTime}</span>
                    </div>
                    <h3 class="post-title" itemprop="headline">
                        <a href="/post/${post.slug}/" itemprop="url">${post.title}</a>
                    </h3>
                    <p class="post-excerpt" itemprop="description">${post.excerpt}</p>
                    <a href="/post/${post.slug}/" class="read-more">Read More →</a>
                    <div class="post-cta">
                        <small>Need worksheets? <a href="https://littlestareducation.com/" target="_blank">Try our free tools</a></small>
                    </div>
                </div>
            </article>
        `).join('');
    }

    // Render latest posts
    renderLatestPosts() {
        const container = document.getElementById('latest-posts');
        if (!container) return;

        const startIndex = (this.currentPage - 1) * this.postsPerPage;
        const endIndex = startIndex + this.postsPerPage;
        const paginatedPosts = this.posts.slice(startIndex, endIndex);

        container.innerHTML = paginatedPosts.map(post => `
            <article class="post-card" itemscope itemtype="https://schema.org/BlogPosting">
                <img src="${post.image}" alt="${post.title}" class="post-image" itemprop="image">
                <div class="post-content">
                    <div class="post-meta">
                        <span class="category-tag">${this.getCategoryName(post.category)}</span>
                        <span>•</span>
                        <span itemprop="author">${post.author}</span>
                        <span>•</span>
                        <time itemprop="datePublished" datetime="${post.date}">${this.formatDate(post.date)}</time>
                    </div>
                    <h3 class="post-title" itemprop="headline">
                        <a href="/post/${post.slug}/" itemprop="url">${post.title}</a>
                    </h3>
                    <p class="post-excerpt" itemprop="description">${post.excerpt}</p>
                    <div class="post-footer">
                        <a href="/post/${post.slug}/" class="read-more">Read Full Article →</a>
                        <div class="post-cta">
                            <small>Get more resources at <a href="https://littlestareducation.com/" target="_blank">LittleStarEducation.com</a></small>
                        </div>
                    </div>
                </div>
            </article>
        `).join('');

        this.renderPagination();
    }

    // Render popular posts in sidebar
    renderPopularPosts() {
        const container = document.getElementById('popular-posts');
        if (!container) return;

        const popularPosts = this.posts
            .sort((a, b) => b.views - a.views)
            .slice(0, 5);

        container.innerHTML = popularPosts.map(post => `
            <div class="popular-post-item">
                <h4><a href="/post/${post.slug}/">${post.title}</a></h4>
                <div class="post-meta">
                    <span>${this.formatDate(post.date)}</span>
                    <span>•</span>
                    <span>${post.views} views</span>
                </div>
            </div>
        `).join('');
    }

    // Render pagination
    renderPagination() {
        const container = document.querySelector('.pagination');
        if (!container) return;

        const totalPages = Math.ceil(this.posts.length / this.postsPerPage);
        if (totalPages <= 1) {
            container.innerHTML = '';
            return;
        }

        let paginationHTML = '<ul class="pagination-list">';
        
        // Previous button
        if (this.currentPage > 1) {
            paginationHTML += `<li><a href="#" data-page="${this.currentPage - 1}" class="pagination-link">← Previous</a></li>`;
        }

        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            const activeClass = i === this.currentPage ? 'active' : '';
            paginationHTML += `<li><a href="#" data-page="${i}" class="pagination-link ${activeClass}">${i}</a></li>`;
        }

        // Next button
        if (this.currentPage < totalPages) {
            paginationHTML += `<li><a href="#" data-page="${this.currentPage + 1}" class="pagination-link">Next →</a></li>`;
        }

        paginationHTML += '</ul>';
        container.innerHTML = paginationHTML;

        // Add event listeners to pagination links
        container.querySelectorAll('.pagination-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const page = parseInt(e.target.dataset.page);
                this.currentPage = page;
                this.renderLatestPosts();
                window.scrollTo({ top: 0, behavior: 'smooth' });
            });
        });
    }

    // Handle search
    handleSearch() {
        const searchInput = document.querySelector('.search-input');
        const query = searchInput.value.trim().toLowerCase();
        
        if (!query) return;

        // Filter posts based on search query
        const filteredPosts = this.posts.filter(post => 
            post.title.toLowerCase().includes(query) ||
            post.excerpt.toLowerCase().includes(query) ||
            post.keywords.some(keyword => keyword.toLowerCase().includes(query))
        );

        // Update display with search results
        this.displaySearchResults(filteredPosts, query);
        
        // Track search for analytics
        if (typeof gtag !== 'undefined') {
            gtag('event', 'search', {
                search_term: query
            });
        }
    }

    // Display search results
    displaySearchResults(results, query) {
        const container = document.getElementById('latest-posts');
        if (!container) return;

        if (results.length === 0) {
            container.innerHTML = `
                <div class="search-no-results">
                    <h3>No results found for "${query}"</h3>
                    <p>Try searching for different keywords or browse our categories.</p>
                    <a href="https://littlestareducation.com/" class="btn btn-primary" target="_blank">
                        Create Custom Worksheets Instead
                    </a>
                </div>
            `;
            return;
        }

        container.innerHTML = `
            <div class="search-results-header">
                <h3>Search Results for "${query}" (${results.length} found)</h3>
            </div>
            ${results.map(post => `
                <article class="post-card">
                    <img src="${post.image}" alt="${post.title}" class="post-image">
                    <div class="post-content">
                        <div class="post-meta">
                            <span class="category-tag">${this.getCategoryName(post.category)}</span>
                            <span>•</span>
                            <span>${post.author}</span>
                            <span>•</span>
                            <span>${this.formatDate(post.date)}</span>
                        </div>
                        <h3 class="post-title">
                            <a href="/post/${post.slug}/">${post.title}</a>
                        </h3>
                        <p class="post-excerpt">${post.excerpt}</p>
                        <a href="/post/${post.slug}/" class="read-more">Read More →</a>
                    </div>
                </article>
            `).join('')}
        `;
    }

    // Handle newsletter signup
    handleNewsletterSignup() {
        const emailInput = document.querySelector('.newsletter-input');
        const email = emailInput.value.trim();
        
        if (!this.isValidEmail(email)) {
            alert('Please enter a valid email address.');
            return;
        }

        // Simulate newsletter signup
        alert('Thank you for subscribing! You\'ll receive weekly educational tips and free resources.');
        emailInput.value = '';
        
        // Track signup for analytics
        if (typeof gtag !== 'undefined') {
            gtag('event', 'newsletter_signup', {
                method: 'blog_sidebar'
            });
        }
    }

    // Setup mobile menu
    setupMobileMenu() {
        const toggle = document.querySelector('.mobile-menu-toggle');
        const menu = document.querySelector('.nav-menu');
        
        if (toggle && menu) {
            toggle.addEventListener('click', () => {
                menu.classList.toggle('active');
                toggle.classList.toggle('active');
            });

            // Close menu when clicking outside
            document.addEventListener('click', (e) => {
                if (!toggle.contains(e.target) && !menu.contains(e.target)) {
                    menu.classList.remove('active');
                    toggle.classList.remove('active');
                }
            });
        }
    }

    // Setup search functionality
    setupSearch() {
        const searchInput = document.querySelector('.search-input');
        if (searchInput) {
            // Add autocomplete suggestions
            searchInput.addEventListener('input', (e) => {
                const query = e.target.value.toLowerCase();
                if (query.length > 2) {
                    this.showSearchSuggestions(query);
                } else {
                    this.hideSearchSuggestions();
                }
            });
        }
    }

    // Setup newsletter
    setupNewsletter() {
        // Add email validation
        const newsletterInput = document.querySelector('.newsletter-input');
        if (newsletterInput) {
            newsletterInput.addEventListener('blur', (e) => {
                const email = e.target.value.trim();
                if (email && !this.isValidEmail(email)) {
                    e.target.setCustomValidity('Please enter a valid email address');
                } else {
                    e.target.setCustomValidity('');
                }
            });
        }
    }

    // Utility functions
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', { 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
        });
    }

    getCategoryName(categoryId) {
        const category = this.categories.find(cat => cat.id === categoryId);
        return category ? category.name : 'Uncategorized';
    }

    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    showSearchSuggestions(query) {
        // Implementation for search suggestions
        console.log('Showing suggestions for:', query);
    }

    hideSearchSuggestions() {
        // Implementation to hide search suggestions
        console.log('Hiding suggestions');
    }
}

// Initialize the blog when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new BlogManager();
});

// Performance optimization: Lazy load images
if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                observer.unobserve(img);
            }
        });
    });

    document.querySelectorAll('img[data-src]').forEach(img => {
        imageObserver.observe(img);
    });
}

// SEO and Analytics Functions
class SEOManager {
    constructor() {
        this.init();
    }

    init() {
        this.trackPageView();
        this.trackOutboundLinks();
        this.generateStructuredData();
    }

    trackPageView() {
        if (typeof gtag !== 'undefined') {
            gtag('config', 'GA_MEASUREMENT_ID', {
                page_title: document.title,
                page_location: window.location.href
            });
        }
    }

    trackOutboundLinks() {
        document.querySelectorAll('a[href*="littlestareducation.com"]').forEach(link => {
            link.addEventListener('click', () => {
                if (typeof gtag !== 'undefined') {
                    gtag('event', 'click', {
                        event_category: 'outbound',
                        event_label: 'main_site_link',
                        transport_type: 'beacon'
                    });
                }
            });
        });
    }

    generateStructuredData() {
        // Add breadcrumb structured data
        const breadcrumbData = {
            "@context": "https://schema.org",
            "@type": "BreadcrumbList",
            "itemListElement": this.getBreadcrumbItems()
        };

        const script = document.createElement('script');
        script.type = 'application/ld+json';
        script.textContent = JSON.stringify(breadcrumbData);
        document.head.appendChild(script);
    }

    getBreadcrumbItems() {
        const path = window.location.pathname;
        const items = [
            {
                "@type": "ListItem",
                "position": 1,
                "name": "Home",
                "item": "https://blog.littlestareducation.com/"
            }
        ];

        if (path.includes('/category/')) {
            const category = path.split('/category/')[1].replace('/', '');
            items.push({
                "@type": "ListItem",
                "position": 2,
                "name": this.getCategoryDisplayName(category),
                "item": `https://blog.littlestareducation.com/category/${category}/`
            });
        }

        return items;
    }

    getCategoryDisplayName(slug) {
        const categories = {
            'teaching-resources': 'Teaching Resources',
            'worksheet-ideas': 'Worksheet Ideas',
            'classroom-management': 'Classroom Management',
            'educational-technology': 'Educational Technology',
            'parent-tips': 'Parent Tips',
            'learning-activities': 'Learning Activities'
        };
        return categories[slug] || 'Category';
    }
}

// Conversion Tracking and Lead Generation
class ConversionManager {
    constructor() {
        this.init();
    }

    init() {
        this.setupExitIntent();
        this.setupScrollTracking();
        this.setupNewsletterPopup();
        this.setupStickyCTA();
        this.trackConversions();
        this.addInlineCtAs();
    }

    // Exit intent detection
    setupExitIntent() {
        let exitIntentShown = false;

        document.addEventListener('mouseleave', (e) => {
            if (e.clientY <= 0 && !exitIntentShown) {
                this.showExitIntentModal();
                exitIntentShown = true;
            }
        });
    }

    // Show exit intent modal
    showExitIntentModal() {
        const modal = document.createElement('div');
        modal.className = 'exit-intent-modal show';
        modal.innerHTML = `
            <div class="exit-intent-content">
                <button class="close-modal" onclick="this.parentElement.parentElement.remove()">&times;</button>
                <h3>Wait! Don't Miss Out on <span class="highlight">Free Educational Resources</span></h3>
                <p>Get instant access to our complete library of worksheets, teaching tips, and educational tools.</p>
                <div style="margin: 1.5rem 0;">
                    <input type="email" placeholder="Enter your email address" style="width: 70%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 4px;">
                    <button class="btn btn-primary" style="margin-left: 0.5rem; padding: 0.75rem 1.5rem;" onclick="this.handleExitIntentSignup()">Get Free Access</button>
                </div>
                <p style="font-size: 0.8rem; color: #666;">Join 10,000+ educators already using our resources</p>
                <div style="margin-top: 1rem;">
                    <a href="https://littlestareducation.com/" target="_blank" class="btn btn-secondary">Browse Free Tools Instead</a>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Track exit intent
        if (typeof gtag !== 'undefined') {
            gtag('event', 'exit_intent_shown', {
                event_category: 'engagement'
            });
        }
    }

    // Setup scroll tracking for engagement
    setupScrollTracking() {
        let scrollMilestones = [25, 50, 75, 90];
        let trackedMilestones = [];

        window.addEventListener('scroll', () => {
            const scrollPercent = (window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100;

            scrollMilestones.forEach(milestone => {
                if (scrollPercent >= milestone && !trackedMilestones.includes(milestone)) {
                    trackedMilestones.push(milestone);

                    // Track scroll depth
                    if (typeof gtag !== 'undefined') {
                        gtag('event', 'scroll_depth', {
                            event_category: 'engagement',
                            value: milestone
                        });
                    }

                    // Show newsletter popup at 50% scroll
                    if (milestone === 50) {
                        setTimeout(() => this.showNewsletterPopup(), 2000);
                    }

                    // Show sticky CTA at 75% scroll
                    if (milestone === 75) {
                        this.showStickyCTA();
                    }
                }
            });
        });
    }

    // Show newsletter popup
    showNewsletterPopup() {
        if (localStorage.getItem('newsletter_popup_shown')) return;

        const popup = document.createElement('div');
        popup.className = 'newsletter-popup';
        popup.innerHTML = `
            <button class="close-popup" onclick="this.parentElement.remove()">&times;</button>
            <h4>🎯 Get Weekly Teaching Tips!</h4>
            <p>Join thousands of educators receiving free resources and expert advice.</p>
            <div class="newsletter-form">
                <input type="email" placeholder="Your email address" class="newsletter-input">
                <button class="btn btn-primary" onclick="this.handlePopupSignup()">Subscribe Free</button>
            </div>
            <div style="margin-top: 0.5rem; text-align: center;">
                <small><a href="https://littlestareducation.com/" target="_blank">Or browse our free tools →</a></small>
            </div>
        `;

        document.body.appendChild(popup);

        setTimeout(() => popup.classList.add('show'), 100);

        localStorage.setItem('newsletter_popup_shown', 'true');
    }

    // Setup sticky CTA
    setupStickyCTA() {
        const stickyCTA = document.createElement('div');
        stickyCTA.className = 'sticky-cta';
        stickyCTA.innerHTML = `
            <span>Need custom worksheets? Create them instantly with our free tools!</span>
            <a href="https://littlestareducation.com/" target="_blank" class="btn">Try Free Tools</a>
            <button class="close-sticky" onclick="this.parentElement.remove()">&times;</button>
        `;

        document.body.appendChild(stickyCTA);
    }

    // Show sticky CTA
    showStickyCTA() {
        const stickyCTA = document.querySelector('.sticky-cta');
        if (stickyCTA) {
            stickyCTA.classList.add('show');
        }
    }

    // Track conversions
    trackConversions() {
        // Track clicks to main site
        document.querySelectorAll('a[href*="littlestareducation.com"]').forEach(link => {
            link.addEventListener('click', () => {
                if (typeof gtag !== 'undefined') {
                    gtag('event', 'main_site_click', {
                        event_category: 'conversion',
                        event_label: link.textContent.trim(),
                        transport_type: 'beacon'
                    });
                }
            });
        });

        // Track newsletter signups
        document.querySelectorAll('.newsletter-form').forEach(form => {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleNewsletterSignup(form);
            });
        });
    }

    // Add inline CTAs to content
    addInlineCtAs() {
        const posts = document.querySelectorAll('.post-content');
        posts.forEach(post => {
            // Add CTA after first paragraph
            const firstParagraph = post.querySelector('p');
            if (firstParagraph) {
                const inlineCTA = document.createElement('div');
                inlineCTA.className = 'inline-cta';
                inlineCTA.innerHTML = `
                    <strong>💡 Pro Tip:</strong> Create custom worksheets for your specific needs with our
                    <a href="https://littlestareducation.com/" target="_blank">free online tools</a>.
                    No design skills required!
                `;
                firstParagraph.after(inlineCTA);
            }

            // Add resource download CTA
            const resourceCTA = document.createElement('div');
            resourceCTA.className = 'resource-download';
            resourceCTA.innerHTML = `
                <h3>🎁 Get More Free Educational Resources</h3>
                <p>Access our complete library of worksheets, teaching guides, and classroom activities.</p>
                <a href="https://littlestareducation.com/" target="_blank" class="btn">
                    Explore Free Tools & Resources
                </a>
            `;
            post.appendChild(resourceCTA);
        });
    }

    // Handle newsletter signup
    handleNewsletterSignup(form) {
        const emailInput = form.querySelector('input[type="email"]');
        const email = emailInput.value.trim();

        if (!this.isValidEmail(email)) {
            alert('Please enter a valid email address.');
            return;
        }

        // Simulate signup process
        alert('Thank you for subscribing! Check your email for a welcome message with free resources.');
        emailInput.value = '';

        // Track signup
        if (typeof gtag !== 'undefined') {
            gtag('event', 'newsletter_signup', {
                event_category: 'conversion',
                method: 'blog_form'
            });
        }

        // Show social proof
        this.showSocialProof();
    }

    // Show social proof notification
    showSocialProof() {
        const socialProof = document.createElement('div');
        socialProof.className = 'social-proof';
        socialProof.innerHTML = `
            🎉 You joined <span class="count">10,247</span> educators getting free resources weekly!
        `;

        document.body.appendChild(socialProof);

        setTimeout(() => {
            socialProof.style.position = 'fixed';
            socialProof.style.top = '20px';
            socialProof.style.right = '20px';
            socialProof.style.zIndex = '1000';
        }, 100);

        setTimeout(() => socialProof.remove(), 5000);
    }

    // Email validation
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
}

// Initialize managers
document.addEventListener('DOMContentLoaded', () => {
    new SEOManager();
    new ConversionManager();
});

// Service Worker for caching (optional)
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
            .then(registration => {
                console.log('SW registered: ', registration);
            })
            .catch(registrationError => {
                console.log('SW registration failed: ', registrationError);
            });
    });
}
