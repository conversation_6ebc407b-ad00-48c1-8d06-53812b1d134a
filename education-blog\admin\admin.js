// Admin Panel JavaScript
class AdminPanel {
    constructor() {
        this.currentSection = 'dashboard';
        this.posts = [];
        this.categories = [];
        this.init();
    }

    init() {
        this.loadData();
        this.setupNavigation();
        this.setupForms();
        this.setupTinyMCE();
        this.renderDashboard();
        this.renderPosts();
        this.renderCategories();
        this.renderSEOChecklist();
        this.renderAnalytics();
    }

    // Load sample data
    loadData() {
        // Load posts from main blog data
        this.posts = [
            {
                id: 1,
                title: "10 Free Printable Math Worksheets for Grade 3 Students",
                slug: "free-printable-math-worksheets-grade-3",
                category: "worksheet-ideas",
                author: "<PERSON>",
                date: "2025-01-15",
                status: "published",
                views: 1250,
                featured: true
            },
            {
                id: 2,
                title: "How to Create Engaging Reading Comprehension Activities",
                slug: "create-engaging-reading-comprehension-activities",
                category: "teaching-resources",
                author: "<PERSON>",
                date: "2025-01-12",
                status: "published",
                views: 980,
                featured: true
            },
            {
                id: 3,
                title: "Best Educational Apps for Remote Learning in 2025",
                slug: "best-educational-apps-remote-learning-2025",
                category: "educational-technology",
                author: "<PERSON> <PERSON>",
                date: "2025-01-10",
                status: "published",
                views: 750,
                featured: false
            }
        ];

        this.categories = [
            { id: 'teaching-resources', name: 'Teaching Resources', posts: 15, description: 'Resources and tips for teachers' },
            { id: 'worksheet-ideas', name: 'Worksheet Ideas', posts: 12, description: 'Creative worksheet concepts and templates' },
            { id: 'classroom-management', name: 'Classroom Management', posts: 8, description: 'Strategies for effective classroom management' },
            { id: 'educational-technology', name: 'Educational Technology', posts: 10, description: 'Technology tools for education' },
            { id: 'parent-tips', name: 'Parent Tips', posts: 6, description: 'Advice for parents supporting learning' },
            { id: 'learning-activities', name: 'Learning Activities', posts: 9, description: 'Fun and educational activities' }
        ];
    }

    // Setup navigation
    setupNavigation() {
        const navLinks = document.querySelectorAll('.admin-nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                if (link.classList.contains('view-site')) return;
                
                e.preventDefault();
                const section = link.dataset.section;
                this.showSection(section);
                
                // Update active nav
                navLinks.forEach(l => l.classList.remove('active'));
                link.classList.add('active');
            });
        });
    }

    // Show specific section
    showSection(sectionName) {
        const sections = document.querySelectorAll('.admin-section');
        sections.forEach(section => section.classList.remove('active'));
        
        const targetSection = document.getElementById(`${sectionName}-section`);
        if (targetSection) {
            targetSection.classList.add('active');
            this.currentSection = sectionName;
        }
    }

    // Setup forms
    setupForms() {
        // Global SEO form
        const globalSeoForm = document.getElementById('global-seo-form');
        if (globalSeoForm) {
            globalSeoForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveGlobalSEO();
            });
        }

        // Analytics form
        const analyticsForm = document.getElementById('analytics-form');
        if (analyticsForm) {
            analyticsForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveAnalyticsSettings();
            });
        }

        // New post form
        const newPostForm = document.getElementById('new-post-form');
        if (newPostForm) {
            newPostForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.createNewPost();
            });
        }

        // Setup real-time SEO preview
        this.setupSEOPreview();
    }

    // Setup TinyMCE editor
    setupTinyMCE() {
        if (typeof tinymce !== 'undefined') {
            tinymce.init({
                selector: '#post-content',
                height: 400,
                menubar: false,
                plugins: [
                    'advlist', 'autolink', 'lists', 'link', 'image', 'charmap',
                    'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
                    'insertdatetime', 'media', 'table', 'help', 'wordcount'
                ],
                toolbar: 'undo redo | blocks | bold italic forecolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | removeformat | help',
                content_style: 'body { font-family: -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; font-size: 14px }',
                setup: (editor) => {
                    editor.on('change', () => {
                        this.updateSEOPreview();
                    });
                }
            });
        }
    }

    // Setup SEO preview
    setupSEOPreview() {
        const titleInput = document.getElementById('post-title');
        const slugInput = document.getElementById('post-slug');
        const excerptInput = document.getElementById('post-excerpt');

        if (titleInput) {
            titleInput.addEventListener('input', () => {
                this.updateSEOPreview();
                this.updateSlug();
                this.updateCharacterCount();
            });
        }

        if (excerptInput) {
            excerptInput.addEventListener('input', () => {
                this.updateSEOPreview();
                this.updateCharacterCount();
            });
        }

        if (slugInput) {
            slugInput.addEventListener('input', () => {
                this.updateSEOPreview();
            });
        }
    }

    // Update SEO preview
    updateSEOPreview() {
        const title = document.getElementById('post-title')?.value || 'Your post title will appear here';
        const slug = document.getElementById('post-slug')?.value || 'your-slug';
        const excerpt = document.getElementById('post-excerpt')?.value || 'Your post excerpt will appear here';

        const previewTitle = document.getElementById('seo-preview-title');
        const previewUrl = document.getElementById('seo-preview-url');
        const previewDescription = document.getElementById('seo-preview-description');

        if (previewTitle) previewTitle.textContent = title;
        if (previewUrl) previewUrl.textContent = `https://blog.littlestareducation.com/post/${slug}/`;
        if (previewDescription) previewDescription.textContent = excerpt;
    }

    // Update character count
    updateCharacterCount() {
        const titleInput = document.getElementById('post-title');
        const excerptInput = document.getElementById('post-excerpt');
        const titleLength = document.getElementById('title-length');
        const excerptLength = document.getElementById('excerpt-length');

        if (titleInput && titleLength) {
            titleLength.textContent = titleInput.value.length;
            titleLength.style.color = titleInput.value.length > 60 ? '#e74c3c' : '#27ae60';
        }

        if (excerptInput && excerptLength) {
            excerptLength.textContent = excerptInput.value.length;
            excerptLength.style.color = excerptInput.value.length > 160 ? '#e74c3c' : '#27ae60';
        }
    }

    // Update slug from title
    updateSlug() {
        const titleInput = document.getElementById('post-title');
        const slugInput = document.getElementById('post-slug');

        if (titleInput && slugInput && !slugInput.value) {
            const slug = titleInput.value
                .toLowerCase()
                .replace(/[^a-z0-9\s-]/g, '')
                .replace(/\s+/g, '-')
                .replace(/-+/g, '-')
                .trim('-');
            slugInput.value = slug;
        }
    }

    // Render dashboard
    renderDashboard() {
        // Update stats
        const totalPosts = document.getElementById('total-posts');
        const totalViews = document.getElementById('total-views');
        const monthlyGrowth = document.getElementById('monthly-growth');
        const mainSiteClicks = document.getElementById('main-site-clicks');

        if (totalPosts) totalPosts.textContent = this.posts.length;
        if (totalViews) totalViews.textContent = this.posts.reduce((sum, post) => sum + post.views, 0).toLocaleString();
        if (monthlyGrowth) monthlyGrowth.textContent = '+23%';
        if (mainSiteClicks) mainSiteClicks.textContent = '342';

        // Render recent activity
        this.renderRecentActivity();
    }

    // Render recent activity
    renderRecentActivity() {
        const activityList = document.getElementById('activity-list');
        if (!activityList) return;

        const activities = [
            { action: 'New post published', item: 'Math Worksheets for Grade 3', time: '2 hours ago' },
            { action: 'Post updated', item: 'Reading Comprehension Activities', time: '5 hours ago' },
            { action: 'New comment', item: 'Science Experiments post', time: '1 day ago' },
            { action: 'SEO optimized', item: 'Educational Apps article', time: '2 days ago' }
        ];

        activityList.innerHTML = activities.map(activity => `
            <div class="activity-item">
                <strong>${activity.action}:</strong> ${activity.item}
                <small style="float: right; color: #7f8c8d;">${activity.time}</small>
            </div>
        `).join('');
    }

    // Render posts table
    renderPosts() {
        const tableBody = document.getElementById('posts-table-body');
        if (!tableBody) return;

        tableBody.innerHTML = this.posts.map(post => `
            <tr>
                <td>
                    <strong>${post.title}</strong>
                    <br><small>/post/${post.slug}/</small>
                </td>
                <td>${this.getCategoryName(post.category)}</td>
                <td>${post.author}</td>
                <td>${this.formatDate(post.date)}</td>
                <td><span class="status-badge status-${post.status}">${post.status}</span></td>
                <td>${post.views.toLocaleString()}</td>
                <td>
                    <a href="#" class="action-btn edit" onclick="editPost(${post.id})">Edit</a>
                    <a href="/post/${post.slug}/" class="action-btn view" target="_blank">View</a>
                    <button class="action-btn delete" onclick="deletePost(${post.id})">Delete</button>
                </td>
            </tr>
        `).join('');
    }

    // Render categories
    renderCategories() {
        const categoriesGrid = document.getElementById('categories-grid');
        if (!categoriesGrid) return;

        categoriesGrid.innerHTML = this.categories.map(category => `
            <div class="category-card">
                <h3>${category.name}</h3>
                <p>${category.description}</p>
                <div class="category-stats">
                    <span>${category.posts} posts</span>
                    <span>Last updated: Today</span>
                </div>
                <div style="margin-top: 1rem;">
                    <button class="action-btn edit" onclick="editCategory('${category.id}')">Edit</button>
                    <button class="action-btn view">View Posts</button>
                </div>
            </div>
        `).join('');
    }

    // Render SEO checklist
    renderSEOChecklist() {
        const checklist = document.getElementById('seo-checklist');
        if (!checklist) return;

        const items = [
            { text: 'XML Sitemap generated and submitted', status: 'completed' },
            { text: 'Robots.txt file configured', status: 'completed' },
            { text: 'Google Analytics installed', status: 'completed' },
            { text: 'Search Console verified', status: 'warning' },
            { text: 'Meta descriptions optimized', status: 'completed' },
            { text: 'Internal linking strategy implemented', status: 'warning' },
            { text: 'Page speed optimized', status: 'error' },
            { text: 'Mobile-friendly design', status: 'completed' }
        ];

        checklist.innerHTML = items.map(item => `
            <div class="checklist-item ${item.status}">
                <span>${item.status === 'completed' ? '✅' : item.status === 'warning' ? '⚠️' : '❌'}</span>
                <span>${item.text}</span>
            </div>
        `).join('');
    }

    // Render analytics
    renderAnalytics() {
        this.renderTopPosts();
        this.renderTrafficSources();
        this.renderConversions();
    }

    // Render top posts
    renderTopPosts() {
        const topPostsList = document.getElementById('top-posts-list');
        if (!topPostsList) return;

        const sortedPosts = [...this.posts].sort((a, b) => b.views - a.views);

        topPostsList.innerHTML = sortedPosts.map((post, index) => `
            <div style="display: flex; justify-content: space-between; padding: 0.5rem 0; border-bottom: 1px solid #dee2e6;">
                <div>
                    <strong>${index + 1}. ${post.title}</strong>
                    <br><small>${this.getCategoryName(post.category)}</small>
                </div>
                <div style="text-align: right;">
                    <strong>${post.views.toLocaleString()}</strong>
                    <br><small>views</small>
                </div>
            </div>
        `).join('');
    }

    // Render traffic sources
    renderTrafficSources() {
        const trafficSources = document.getElementById('traffic-sources');
        if (!trafficSources) return;

        const sources = [
            { name: 'Google Search', percentage: 45, visits: 2510 },
            { name: 'Direct', percentage: 25, visits: 1395 },
            { name: 'Social Media', percentage: 15, visits: 837 },
            { name: 'Referrals', percentage: 10, visits: 558 },
            { name: 'Email', percentage: 5, visits: 279 }
        ];

        trafficSources.innerHTML = sources.map(source => `
            <div style="display: flex; justify-content: space-between; align-items: center; padding: 0.5rem 0;">
                <div>
                    <strong>${source.name}</strong>
                    <div style="width: 100px; height: 8px; background: #dee2e6; border-radius: 4px; margin-top: 4px;">
                        <div style="width: ${source.percentage}%; height: 100%; background: #3498db; border-radius: 4px;"></div>
                    </div>
                </div>
                <div style="text-align: right;">
                    <strong>${source.percentage}%</strong>
                    <br><small>${source.visits} visits</small>
                </div>
            </div>
        `).join('');
    }

    // Render conversions
    renderConversions() {
        const conversionsData = document.getElementById('conversions-data');
        if (!conversionsData) return;

        conversionsData.innerHTML = `
            <div style="text-align: center;">
                <h4 style="margin: 0; color: #3498db;">342</h4>
                <p style="margin: 0.5rem 0; color: #7f8c8d;">Clicks to Main Site</p>
                <div style="font-size: 0.9rem; color: #27ae60;">+18% this month</div>
            </div>
            <hr style="margin: 1rem 0;">
            <div style="font-size: 0.9rem;">
                <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                    <span>Worksheet Downloads:</span>
                    <strong>156</strong>
                </div>
                <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                    <span>Tool Usage:</span>
                    <strong>89</strong>
                </div>
                <div style="display: flex; justify-content: space-between;">
                    <span>Newsletter Signups:</span>
                    <strong>97</strong>
                </div>
            </div>
        `;
    }

    // Create new post
    createNewPost() {
        const formData = {
            title: document.getElementById('post-title').value,
            slug: document.getElementById('post-slug').value,
            excerpt: document.getElementById('post-excerpt').value,
            category: document.getElementById('post-category').value,
            author: document.getElementById('post-author').value,
            keywords: document.getElementById('post-keywords').value,
            image: document.getElementById('post-image').value,
            content: tinymce.get('post-content').getContent(),
            featured: document.getElementById('post-featured').checked,
            status: document.getElementById('post-status').value
        };

        // Validate required fields
        if (!formData.title || !formData.excerpt || !formData.category || !formData.content) {
            alert('Please fill in all required fields.');
            return;
        }

        // Create new post object
        const newPost = {
            id: this.posts.length + 1,
            ...formData,
            date: new Date().toISOString().split('T')[0],
            views: 0
        };

        // Add to posts array
        this.posts.unshift(newPost);

        // Update displays
        this.renderPosts();
        this.renderDashboard();

        // Close modal
        closeModal('new-post-modal');

        // Reset form
        document.getElementById('new-post-form').reset();
        tinymce.get('post-content').setContent('');

        alert('Post created successfully!');
    }

    // Save global SEO settings
    saveGlobalSEO() {
        const settings = {
            title: document.getElementById('site-title').value,
            description: document.getElementById('site-description').value,
            keywords: document.getElementById('site-keywords').value
        };

        // Save settings (in real app, this would go to backend)
        localStorage.setItem('globalSEO', JSON.stringify(settings));
        alert('SEO settings saved successfully!');
    }

    // Save analytics settings
    saveAnalyticsSettings() {
        const settings = {
            gaTrackingId: document.getElementById('ga-tracking-id').value,
            gscVerification: document.getElementById('gsc-verification').value
        };

        // Save settings
        localStorage.setItem('analyticsSettings', JSON.stringify(settings));
        alert('Analytics settings saved successfully!');
    }

    // Utility functions
    getCategoryName(categoryId) {
        const category = this.categories.find(cat => cat.id === categoryId);
        return category ? category.name : 'Uncategorized';
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', { 
            year: 'numeric', 
            month: 'short', 
            day: 'numeric' 
        });
    }
}

// Global functions for button actions
function showNewPostForm() {
    showModal('new-post-modal');
}

function showNewCategoryForm() {
    alert('Category management feature coming soon!');
}

function showModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.add('active');
        document.body.style.overflow = 'hidden';
    }
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('active');
        document.body.style.overflow = '';
    }
}

function editPost(postId) {
    alert(`Edit post ${postId} - Feature coming soon!`);
}

function deletePost(postId) {
    if (confirm('Are you sure you want to delete this post?')) {
        alert(`Delete post ${postId} - Feature coming soon!`);
    }
}

function editCategory(categoryId) {
    alert(`Edit category ${categoryId} - Feature coming soon!`);
}

function generateSitemap() {
    alert('Sitemap generated successfully! Check /sitemap.xml');
}

function generateRobotsTxt() {
    alert('Robots.txt generated successfully! Check /robots.txt');
}

function optimizeImages() {
    alert('Image optimization started. This may take a few minutes.');
}

function checkSEO() {
    alert('SEO check completed! See results in the SEO checklist.');
}

function checkBrokenLinks() {
    alert('Broken link check started. Results will be emailed to you.');
}

function analyzePageSpeed() {
    alert('Page speed analysis started. Check Google PageSpeed Insights for detailed results.');
}

// Initialize admin panel when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new AdminPanel();
});

// Close modals when clicking outside
document.addEventListener('click', (e) => {
    if (e.target.classList.contains('modal')) {
        e.target.classList.remove('active');
        document.body.style.overflow = '';
    }
});
