// Admin Panel JavaScript
class AdminPanel {
    constructor() {
        this.currentSection = 'dashboard';
        this.posts = [];
        this.categories = [];
        this.init();
    }

    init() {
        this.loadData();
        this.setupNavigation();
        this.setupForms();
        this.setupTinyMCE();
        this.renderDashboard();
        this.renderPosts();
        this.renderCategories();
        this.renderSEOChecklist();
        this.renderAnalytics();
    }

    // Load sample data
    loadData() {
        // Load posts from main blog data
        this.posts = [
            {
                id: 1,
                title: "10 Free Printable Math Worksheets for Grade 3 Students",
                slug: "free-printable-math-worksheets-grade-3",
                category: "worksheet-ideas",
                author: "<PERSON>",
                date: "2025-01-15",
                status: "published",
                views: 1250,
                featured: true
            },
            {
                id: 2,
                title: "How to Create Engaging Reading Comprehension Activities",
                slug: "create-engaging-reading-comprehension-activities",
                category: "teaching-resources",
                author: "<PERSON>",
                date: "2025-01-12",
                status: "published",
                views: 980,
                featured: true
            },
            {
                id: 3,
                title: "Best Educational Apps for Remote Learning in 2025",
                slug: "best-educational-apps-remote-learning-2025",
                category: "educational-technology",
                author: "<PERSON> <PERSON>",
                date: "2025-01-10",
                status: "published",
                views: 750,
                featured: false
            }
        ];

        this.categories = [
            { id: 'teaching-resources', name: 'Teaching Resources', posts: 15, description: 'Resources and tips for teachers' },
            { id: 'worksheet-ideas', name: 'Worksheet Ideas', posts: 12, description: 'Creative worksheet concepts and templates' },
            { id: 'classroom-management', name: 'Classroom Management', posts: 8, description: 'Strategies for effective classroom management' },
            { id: 'educational-technology', name: 'Educational Technology', posts: 10, description: 'Technology tools for education' },
            { id: 'parent-tips', name: 'Parent Tips', posts: 6, description: 'Advice for parents supporting learning' },
            { id: 'learning-activities', name: 'Learning Activities', posts: 9, description: 'Fun and educational activities' }
        ];
    }

    // Setup navigation
    setupNavigation() {
        const navLinks = document.querySelectorAll('.admin-nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                if (link.classList.contains('view-site')) return;
                
                e.preventDefault();
                const section = link.dataset.section;
                this.showSection(section);
                
                // Update active nav
                navLinks.forEach(l => l.classList.remove('active'));
                link.classList.add('active');
            });
        });
    }

    // Show specific section
    showSection(sectionName) {
        const sections = document.querySelectorAll('.admin-section');
        sections.forEach(section => section.classList.remove('active'));
        
        const targetSection = document.getElementById(`${sectionName}-section`);
        if (targetSection) {
            targetSection.classList.add('active');
            this.currentSection = sectionName;
        }
    }

    // Setup forms
    setupForms() {
        // Global SEO form
        const globalSeoForm = document.getElementById('global-seo-form');
        if (globalSeoForm) {
            globalSeoForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveGlobalSEO();
            });
        }

        // Analytics form
        const analyticsForm = document.getElementById('analytics-form');
        if (analyticsForm) {
            analyticsForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveAnalyticsSettings();
            });
        }

        // New post form
        const newPostForm = document.getElementById('new-post-form');
        if (newPostForm) {
            newPostForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.createNewPost();
            });
        }

        // Setup real-time SEO preview
        this.setupSEOPreview();
    }

    // Setup TinyMCE editor
    setupTinyMCE() {
        if (typeof tinymce !== 'undefined') {
            tinymce.init({
                selector: '#post-content',
                height: 400,
                menubar: false,
                plugins: [
                    'advlist', 'autolink', 'lists', 'link', 'image', 'charmap',
                    'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
                    'insertdatetime', 'media', 'table', 'help', 'wordcount'
                ],
                toolbar: 'undo redo | blocks | bold italic forecolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | removeformat | help',
                content_style: 'body { font-family: -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; font-size: 14px }',
                setup: (editor) => {
                    editor.on('change', () => {
                        this.updateSEOPreview();
                    });
                }
            });
        }
    }

    // Setup SEO preview
    setupSEOPreview() {
        const titleInput = document.getElementById('post-title');
        const slugInput = document.getElementById('post-slug');
        const excerptInput = document.getElementById('post-excerpt');

        if (titleInput) {
            titleInput.addEventListener('input', () => {
                this.updateSEOPreview();
                this.updateSlug();
                this.updateCharacterCount();
            });
        }

        if (excerptInput) {
            excerptInput.addEventListener('input', () => {
                this.updateSEOPreview();
                this.updateCharacterCount();
            });
        }

        if (slugInput) {
            slugInput.addEventListener('input', () => {
                this.updateSEOPreview();
            });
        }
    }

    // Update SEO preview
    updateSEOPreview() {
        const title = document.getElementById('post-title')?.value || 'Your post title will appear here';
        const slug = document.getElementById('post-slug')?.value || 'your-slug';
        const excerpt = document.getElementById('post-excerpt')?.value || 'Your post excerpt will appear here';

        const previewTitle = document.getElementById('seo-preview-title');
        const previewUrl = document.getElementById('seo-preview-url');
        const previewDescription = document.getElementById('seo-preview-description');

        if (previewTitle) previewTitle.textContent = title;
        if (previewUrl) previewUrl.textContent = `https://blog.littlestareducation.com/post/${slug}/`;
        if (previewDescription) previewDescription.textContent = excerpt;
    }

    // Update character count
    updateCharacterCount() {
        const titleInput = document.getElementById('post-title');
        const excerptInput = document.getElementById('post-excerpt');
        const titleLength = document.getElementById('title-length');
        const excerptLength = document.getElementById('excerpt-length');

        if (titleInput && titleLength) {
            titleLength.textContent = titleInput.value.length;
            titleLength.style.color = titleInput.value.length > 60 ? '#e74c3c' : '#27ae60';
        }

        if (excerptInput && excerptLength) {
            excerptLength.textContent = excerptInput.value.length;
            excerptLength.style.color = excerptInput.value.length > 160 ? '#e74c3c' : '#27ae60';
        }
    }

    // Update slug from title
    updateSlug() {
        const titleInput = document.getElementById('post-title');
        const slugInput = document.getElementById('post-slug');

        if (titleInput && slugInput && !slugInput.value) {
            const slug = titleInput.value
                .toLowerCase()
                .replace(/[^a-z0-9\s-]/g, '')
                .replace(/\s+/g, '-')
                .replace(/-+/g, '-')
                .trim('-');
            slugInput.value = slug;
        }
    }

    // Render dashboard
    renderDashboard() {
        // Update stats
        const totalPosts = document.getElementById('total-posts');
        const totalViews = document.getElementById('total-views');
        const monthlyGrowth = document.getElementById('monthly-growth');
        const mainSiteClicks = document.getElementById('main-site-clicks');

        if (totalPosts) totalPosts.textContent = this.posts.length;
        if (totalViews) totalViews.textContent = this.posts.reduce((sum, post) => sum + post.views, 0).toLocaleString();
        if (monthlyGrowth) monthlyGrowth.textContent = '+23%';
        if (mainSiteClicks) mainSiteClicks.textContent = '342';

        // Render recent activity
        this.renderRecentActivity();
    }

    // Render recent activity
    renderRecentActivity() {
        const activityList = document.getElementById('activity-list');
        if (!activityList) return;

        const activities = [
            { action: 'New post published', item: 'Math Worksheets for Grade 3', time: '2 hours ago' },
            { action: 'Post updated', item: 'Reading Comprehension Activities', time: '5 hours ago' },
            { action: 'New comment', item: 'Science Experiments post', time: '1 day ago' },
            { action: 'SEO optimized', item: 'Educational Apps article', time: '2 days ago' }
        ];

        activityList.innerHTML = activities.map(activity => `
            <div class="activity-item">
                <strong>${activity.action}:</strong> ${activity.item}
                <small style="float: right; color: #7f8c8d;">${activity.time}</small>
            </div>
        `).join('');
    }

    // Render posts table
    renderPosts() {
        const tableBody = document.getElementById('posts-table-body');
        if (!tableBody) return;

        tableBody.innerHTML = this.posts.map(post => `
            <tr>
                <td>
                    <strong>${post.title}</strong>
                    <br><small>/post/${post.slug}/</small>
                </td>
                <td>${this.getCategoryName(post.category)}</td>
                <td>${post.author}</td>
                <td>${this.formatDate(post.date)}</td>
                <td><span class="status-badge status-${post.status}">${post.status}</span></td>
                <td>${post.views.toLocaleString()}</td>
                <td>
                    <a href="#" class="action-btn edit" onclick="editPost(${post.id})">Edit</a>
                    <a href="/post/${post.slug}/" class="action-btn view" target="_blank">View</a>
                    <button class="action-btn delete" onclick="deletePost(${post.id})">Delete</button>
                </td>
            </tr>
        `).join('');
    }

    // Render categories
    renderCategories() {
        const categoriesGrid = document.getElementById('categories-grid');
        if (!categoriesGrid) return;

        categoriesGrid.innerHTML = this.categories.map(category => `
            <div class="category-card">
                <h3>${category.name}</h3>
                <p>${category.description}</p>
                <div class="category-stats">
                    <span>${category.posts} posts</span>
                    <span>Last updated: Today</span>
                </div>
                <div style="margin-top: 1rem;">
                    <button class="action-btn edit" onclick="editCategory('${category.id}')">Edit</button>
                    <button class="action-btn view">View Posts</button>
                </div>
            </div>
        `).join('');
    }

    // Render SEO checklist
    renderSEOChecklist() {
        const checklist = document.getElementById('seo-checklist');
        if (!checklist) return;

        const items = [
            { text: 'XML Sitemap generated and submitted', status: 'completed' },
            { text: 'Robots.txt file configured', status: 'completed' },
            { text: 'Google Analytics installed', status: 'completed' },
            { text: 'Search Console verified', status: 'warning' },
            { text: 'Meta descriptions optimized', status: 'completed' },
            { text: 'Internal linking strategy implemented', status: 'warning' },
            { text: 'Page speed optimized', status: 'error' },
            { text: 'Mobile-friendly design', status: 'completed' }
        ];

        checklist.innerHTML = items.map(item => `
            <div class="checklist-item ${item.status}">
                <span>${item.status === 'completed' ? '✅' : item.status === 'warning' ? '⚠️' : '❌'}</span>
                <span>${item.text}</span>
            </div>
        `).join('');
    }

    // Render analytics
    renderAnalytics() {
        this.renderTopPosts();
        this.renderTrafficSources();
        this.renderConversions();
    }

    // Render top posts
    renderTopPosts() {
        const topPostsList = document.getElementById('top-posts-list');
        if (!topPostsList) return;

        const sortedPosts = [...this.posts].sort((a, b) => b.views - a.views);

        topPostsList.innerHTML = sortedPosts.map((post, index) => `
            <div style="display: flex; justify-content: space-between; padding: 0.5rem 0; border-bottom: 1px solid #dee2e6;">
                <div>
                    <strong>${index + 1}. ${post.title}</strong>
                    <br><small>${this.getCategoryName(post.category)}</small>
                </div>
                <div style="text-align: right;">
                    <strong>${post.views.toLocaleString()}</strong>
                    <br><small>views</small>
                </div>
            </div>
        `).join('');
    }

    // Render traffic sources
    renderTrafficSources() {
        const trafficSources = document.getElementById('traffic-sources');
        if (!trafficSources) return;

        const sources = [
            { name: 'Google Search', percentage: 45, visits: 2510 },
            { name: 'Direct', percentage: 25, visits: 1395 },
            { name: 'Social Media', percentage: 15, visits: 837 },
            { name: 'Referrals', percentage: 10, visits: 558 },
            { name: 'Email', percentage: 5, visits: 279 }
        ];

        trafficSources.innerHTML = sources.map(source => `
            <div style="display: flex; justify-content: space-between; align-items: center; padding: 0.5rem 0;">
                <div>
                    <strong>${source.name}</strong>
                    <div style="width: 100px; height: 8px; background: #dee2e6; border-radius: 4px; margin-top: 4px;">
                        <div style="width: ${source.percentage}%; height: 100%; background: #3498db; border-radius: 4px;"></div>
                    </div>
                </div>
                <div style="text-align: right;">
                    <strong>${source.percentage}%</strong>
                    <br><small>${source.visits} visits</small>
                </div>
            </div>
        `).join('');
    }

    // Render conversions
    renderConversions() {
        const conversionsData = document.getElementById('conversions-data');
        if (!conversionsData) return;

        conversionsData.innerHTML = `
            <div style="text-align: center;">
                <h4 style="margin: 0; color: #3498db;">342</h4>
                <p style="margin: 0.5rem 0; color: #7f8c8d;">Clicks to Main Site</p>
                <div style="font-size: 0.9rem; color: #27ae60;">+18% this month</div>
            </div>
            <hr style="margin: 1rem 0;">
            <div style="font-size: 0.9rem;">
                <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                    <span>Worksheet Downloads:</span>
                    <strong>156</strong>
                </div>
                <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                    <span>Tool Usage:</span>
                    <strong>89</strong>
                </div>
                <div style="display: flex; justify-content: space-between;">
                    <span>Newsletter Signups:</span>
                    <strong>97</strong>
                </div>
            </div>
        `;
    }

    // Create new post
    createNewPost() {
        const formData = {
            title: document.getElementById('post-title').value,
            slug: document.getElementById('post-slug').value,
            excerpt: document.getElementById('post-excerpt').value,
            category: document.getElementById('post-category').value,
            author: document.getElementById('post-author').value,
            keywords: document.getElementById('post-keywords').value,
            image: document.getElementById('post-image').value,
            content: tinymce.get('post-content').getContent(),
            featured: document.getElementById('post-featured').checked,
            status: document.getElementById('post-status').value
        };

        // Validate required fields
        if (!formData.title || !formData.excerpt || !formData.category || !formData.content) {
            alert('Please fill in all required fields.');
            return;
        }

        // Auto-generate missing fields
        if (!formData.slug) {
            formData.slug = this.generateSlug(formData.title);
        }
        if (!formData.keywords) {
            formData.keywords = this.generateKeywords(formData.title, formData.category);
        }
        if (!formData.image) {
            formData.image = this.getDefaultImage(formData.category);
        }

        // Create new post object
        const newPost = {
            id: this.posts.length + 1,
            ...formData,
            date: new Date().toISOString().split('T')[0],
            views: 0,
            metaTitle: this.generateMetaTitle(formData.title),
            metaDescription: formData.excerpt,
            readTime: this.calculateReadTime(formData.content)
        };

        // Generate the actual HTML file
        this.generatePostHTML(newPost);

        // Add to posts array
        this.posts.unshift(newPost);

        // Update displays
        this.renderPosts();
        this.renderDashboard();

        // Close modal
        closeModal('new-post-modal');

        // Reset form
        document.getElementById('new-post-form').reset();
        tinymce.get('post-content').setContent('');

        // Show success message with link
        this.showSuccessMessage(newPost);
    }

    // Generate post HTML file
    generatePostHTML(post) {
        const htmlContent = this.createPostTemplate(post);

        // In a real implementation, this would save to the server
        // For demo purposes, we'll show the generated HTML
        console.log('Generated HTML for post:', post.slug);

        // Store in localStorage for demo
        localStorage.setItem(`post_${post.slug}`, htmlContent);

        return htmlContent;
    }

    // Create post template
    createPostTemplate(post) {
        const template = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${post.metaTitle}</title>
    <meta name="description" content="${post.metaDescription}">
    <meta name="keywords" content="${post.keywords}">
    <meta name="author" content="${post.author}">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="https://blog.littlestareducation.com/post/${post.slug}/">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="${post.title}">
    <meta property="og:description" content="${post.excerpt}">
    <meta property="og:type" content="article">
    <meta property="og:url" content="https://blog.littlestareducation.com/post/${post.slug}/">
    <meta property="og:image" content="${post.image}">
    <meta property="article:author" content="${post.author}">
    <meta property="article:published_time" content="${post.date}T10:00:00Z">
    <meta property="article:section" content="${this.getCategoryName(post.category)}">

    <!-- Stylesheets -->
    <link rel="stylesheet" href="../../assets/css/styles.css">

    <!-- Schema.org Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "BlogPosting",
        "headline": "${post.title}",
        "description": "${post.excerpt}",
        "image": "${post.image}",
        "author": {
            "@type": "Person",
            "name": "${post.author}"
        },
        "publisher": {
            "@type": "Organization",
            "name": "Little Star Education",
            "logo": {
                "@type": "ImageObject",
                "url": "https://blog.littlestareducation.com/assets/images/logo.png"
            }
        },
        "datePublished": "${post.date}T10:00:00Z",
        "dateModified": "${post.date}T10:00:00Z",
        "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": "https://blog.littlestareducation.com/post/${post.slug}/"
        },
        "articleSection": "${this.getCategoryName(post.category)}",
        "keywords": [${post.keywords.split(',').map(k => `"${k.trim()}"`).join(', ')}]
    }
    </script>
</head>
<body>
    <!-- Header -->
    <header class="site-header">
        <div class="container">
            <div class="header-content">
                <div class="site-branding">
                    <a href="../../" class="logo-link">
                        <img src="../../assets/images/logo.png" alt="Little Star Education" class="logo">
                        <div class="site-title">
                            <h1>Little Star Education Blog</h1>
                            <p class="tagline">Educational Resources & Teaching Tips</p>
                        </div>
                    </a>
                </div>
                <nav class="main-navigation">
                    <ul class="nav-menu">
                        <li><a href="../../" class="nav-link">Home</a></li>
                        <li><a href="../../category/${post.category}/" class="nav-link">${this.getCategoryName(post.category)}</a></li>
                        <li><a href="https://littlestareducation.com/" class="nav-link cta-link" target="_blank">Free Tools</a></li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>

    <!-- Breadcrumb -->
    <nav class="breadcrumb">
        <div class="container">
            <ol class="breadcrumb-list">
                <li><a href="../../">Home</a></li>
                <li><a href="../../category/${post.category}/">${this.getCategoryName(post.category)}</a></li>
                <li aria-current="page">${post.title}</li>
            </ol>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <div class="content-layout">
                <article class="post-content" itemscope itemtype="https://schema.org/BlogPosting">
                    <header class="post-header">
                        <div class="post-meta">
                            <span class="category-tag">${this.getCategoryName(post.category)}</span>
                            <span>•</span>
                            <span itemprop="author">${post.author}</span>
                            <span>•</span>
                            <time itemprop="datePublished" datetime="${post.date}">${this.formatDate(post.date)}</time>
                            <span>•</span>
                            <span>${post.readTime}</span>
                        </div>
                        <h1 class="post-title" itemprop="headline">${post.title}</h1>
                        <p class="post-excerpt" itemprop="description">${post.excerpt}</p>
                        <img src="${post.image}" alt="${post.title}" class="post-image" itemprop="image">
                    </header>

                    <div class="post-body" itemprop="articleBody">
                        ${this.enhanceContent(post.content)}
                    </div>

                    <footer class="post-footer">
                        <div class="post-tags">
                            ${post.keywords.split(',').map(keyword =>
                                `<span class="tag">${keyword.trim()}</span>`
                            ).join('')}
                        </div>

                        <div class="author-bio">
                            <h4>About the Author</h4>
                            <p><strong>${post.author}</strong> is an education specialist passionate about creating engaging learning experiences and sharing valuable resources with teachers and parents.</p>
                        </div>
                    </footer>
                </article>

                <!-- Sidebar -->
                <aside class="sidebar">
                    <div class="widget newsletter-widget">
                        <h3>Get More Free Resources</h3>
                        <p>Subscribe for weekly educational tips and free resources!</p>
                        <form class="newsletter-form">
                            <input type="email" class="newsletter-input" placeholder="Your email address" required>
                            <button type="submit" class="btn btn-primary">Subscribe Free</button>
                        </form>
                    </div>

                    <div class="widget cta-widget">
                        <h3>Need Custom Materials?</h3>
                        <p>Create personalized worksheets and educational content with our free tools.</p>
                        <a href="https://littlestareducation.com/" class="btn btn-primary" target="_blank">Try Free Tools</a>
                    </div>
                </aside>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="site-footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-widgets">
                    <div class="footer-widget">
                        <h4>About Little Star Education</h4>
                        <p>We provide free educational resources and tools to help teachers and parents create engaging learning experiences.</p>
                        <a href="https://littlestareducation.com/" target="_blank">Visit Main Site</a>
                    </div>
                </div>
                <div class="footer-bottom">
                    <p>&copy; 2025 Little Star Education Blog. All rights reserved. |
                       <a href="https://littlestareducation.com/" target="_blank">LittleStarEducation.com</a>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <script src="../../assets/js/main.js"></script>
    <script src="../../analytics.js"></script>
</body>
</html>`;

        return template;
    }

    // Save global SEO settings
    saveGlobalSEO() {
        const settings = {
            title: document.getElementById('site-title').value,
            description: document.getElementById('site-description').value,
            keywords: document.getElementById('site-keywords').value
        };

        // Save settings (in real app, this would go to backend)
        localStorage.setItem('globalSEO', JSON.stringify(settings));
        alert('SEO settings saved successfully!');
    }

    // Save analytics settings
    saveAnalyticsSettings() {
        const settings = {
            gaTrackingId: document.getElementById('ga-tracking-id').value,
            gscVerification: document.getElementById('gsc-verification').value
        };

        // Save settings
        localStorage.setItem('analyticsSettings', JSON.stringify(settings));
        alert('Analytics settings saved successfully!');
    }

    // Content enhancement functions
    enhanceContent(content) {
        // Add strategic CTAs throughout the content
        let enhancedContent = content;

        // Add inline CTA after first paragraph
        enhancedContent = enhancedContent.replace(
            /<\/p>/,
            `</p>
            <div class="inline-cta">
                <strong>💡 Pro Tip:</strong> Create custom educational materials with our
                <a href="https://littlestareducation.com/" target="_blank">free online tools</a>.
                No design skills required!
            </div>`
        );

        // Add conversion banner before conclusion
        if (enhancedContent.includes('<h2>Conclusion</h2>') || enhancedContent.includes('<h3>Conclusion</h3>')) {
            enhancedContent = enhancedContent.replace(
                /<h[23]>Conclusion<\/h[23]>/,
                `<div class="conversion-banner">
                    <h3>🚀 Get More Educational Resources</h3>
                    <p>Access our complete library of teaching tools and classroom materials.</p>
                    <a href="https://littlestareducation.com/" target="_blank" class="btn">Explore Free Tools</a>
                </div>
                <h2>Conclusion</h2>`
            );
        } else {
            // Add at the end if no conclusion section
            enhancedContent += `
            <div class="resource-download">
                <h3>🎁 Get More Free Educational Resources</h3>
                <p>Join thousands of educators using our comprehensive library of teaching tools.</p>
                <a href="https://littlestareducation.com/" target="_blank" class="btn">Access Free Tools</a>
            </div>`;
        }

        return enhancedContent;
    }

    // Auto-generation helper functions
    generateSlug(title) {
        return title
            .toLowerCase()
            .replace(/[^a-z0-9\s-]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .trim('-')
            .substring(0, 60);
    }

    generateKeywords(title, category) {
        const categoryKeywords = {
            'teaching-resources': ['teaching tips', 'classroom resources', 'education strategies'],
            'worksheet-ideas': ['free worksheets', 'printable worksheets', 'educational worksheets'],
            'classroom-management': ['classroom management', 'teaching strategies', 'student behavior'],
            'educational-technology': ['educational apps', 'teaching technology', 'digital learning'],
            'parent-tips': ['parenting tips', 'home learning', 'educational support'],
            'learning-activities': ['learning activities', 'educational games', 'fun learning']
        };

        const baseKeywords = categoryKeywords[category] || ['education', 'learning', 'teaching'];
        const titleWords = title.toLowerCase().split(' ').filter(word => word.length > 3);

        return [...baseKeywords, ...titleWords.slice(0, 3)].join(', ');
    }

    generateMetaTitle(title) {
        if (title.length <= 50) {
            return `${title} | Little Star Education`;
        }
        return `${title.substring(0, 50)}... | Little Star Education`;
    }

    getDefaultImage(category) {
        const defaultImages = {
            'teaching-resources': '../../assets/images/teaching-resources-default.jpg',
            'worksheet-ideas': '../../assets/images/worksheets-default.jpg',
            'classroom-management': '../../assets/images/classroom-default.jpg',
            'educational-technology': '../../assets/images/technology-default.jpg',
            'parent-tips': '../../assets/images/parent-tips-default.jpg',
            'learning-activities': '../../assets/images/activities-default.jpg'
        };

        return defaultImages[category] || '../../assets/images/blog-default.jpg';
    }

    calculateReadTime(content) {
        const wordsPerMinute = 200;
        const textContent = content.replace(/<[^>]*>/g, ''); // Remove HTML tags
        const wordCount = textContent.split(/\s+/).length;
        const readTime = Math.ceil(wordCount / wordsPerMinute);
        return `${readTime} min read`;
    }

    showSuccessMessage(post) {
        // Show in a modal
        const successModal = document.createElement('div');
        successModal.className = 'modal active';
        successModal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Post Created Successfully!</h3>
                    <button class="modal-close" onclick="this.parentElement.parentElement.parentElement.remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <p><strong>Title:</strong> ${post.title}</p>
                    <p><strong>URL:</strong> /post/${post.slug}/</p>
                    <p><strong>Status:</strong> ${post.status}</p>
                    <p><strong>SEO Score:</strong> ${this.calculateSEOScore(post)}/100</p>

                    <h4>Next Steps:</h4>
                    <ol>
                        <li>Download the HTML file below</li>
                        <li>Create folder: <code>/post/${post.slug}/</code></li>
                        <li>Save as <code>index.html</code> in that folder</li>
                        <li>Upload to your web server</li>
                        <li>Update your sitemap.xml</li>
                    </ol>

                    <div style="margin-top: 1rem;">
                        <button onclick="downloadPostHTML('${post.slug}')" class="btn btn-primary">📄 Download HTML File</button>
                        <button onclick="copyPostHTML('${post.slug}')" class="btn btn-secondary">📋 Copy HTML Code</button>
                        <button onclick="generateSitemapEntry('${post.slug}')" class="btn btn-secondary">🗺️ Get Sitemap Entry</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(successModal);
    }

    calculateSEOScore(post) {
        let score = 0;

        // Title optimization (20 points)
        if (post.title.length >= 30 && post.title.length <= 60) score += 20;
        else if (post.title.length >= 20) score += 10;

        // Meta description (20 points)
        if (post.excerpt.length >= 120 && post.excerpt.length <= 160) score += 20;
        else if (post.excerpt.length >= 100) score += 10;

        // Content length (20 points)
        const wordCount = post.content.replace(/<[^>]*>/g, '').split(/\s+/).length;
        if (wordCount >= 1000) score += 20;
        else if (wordCount >= 500) score += 15;
        else if (wordCount >= 300) score += 10;

        // Keywords (15 points)
        if (post.keywords && post.keywords.split(',').length >= 3) score += 15;
        else if (post.keywords) score += 8;

        // Image (10 points)
        if (post.image) score += 10;

        // Internal linking (15 points)
        if (post.content.includes('littlestareducation.com')) score += 15;

        return Math.min(score, 100);
    }

    // Utility functions
    getCategoryName(categoryId) {
        const category = this.categories.find(cat => cat.id === categoryId);
        return category ? category.name : 'Uncategorized';
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }
}

// Global functions for button actions
function showNewPostForm() {
    showModal('new-post-modal');
}

function showNewCategoryForm() {
    alert('Category management feature coming soon!');
}

function showModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.add('active');
        document.body.style.overflow = 'hidden';
    }
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('active');
        document.body.style.overflow = '';
    }
}

function editPost(postId) {
    alert(`Edit post ${postId} - Feature coming soon!`);
}

function deletePost(postId) {
    if (confirm('Are you sure you want to delete this post?')) {
        alert(`Delete post ${postId} - Feature coming soon!`);
    }
}

function editCategory(categoryId) {
    alert(`Edit category ${categoryId} - Feature coming soon!`);
}

function generateSitemap() {
    alert('Sitemap generated successfully! Check /sitemap.xml');
}

function generateRobotsTxt() {
    alert('Robots.txt generated successfully! Check /robots.txt');
}

function optimizeImages() {
    alert('Image optimization started. This may take a few minutes.');
}

function checkSEO() {
    alert('SEO check completed! See results in the SEO checklist.');
}

function checkBrokenLinks() {
    alert('Broken link check started. Results will be emailed to you.');
}

function analyzePageSpeed() {
    alert('Page speed analysis started. Check Google PageSpeed Insights for detailed results.');
}

// Global functions for post management
function downloadPostHTML(slug) {
    const htmlContent = localStorage.getItem(`post_${slug}`);
    if (!htmlContent) {
        alert('Post HTML not found. Please regenerate the post.');
        return;
    }

    // Create download link
    const blob = new Blob([htmlContent], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${slug}-index.html`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    alert('HTML file downloaded! Create a folder named "' + slug + '" and save this as "index.html" inside it.');
}

function copyPostHTML(slug) {
    const htmlContent = localStorage.getItem(`post_${slug}`);
    if (!htmlContent) {
        alert('Post HTML not found. Please regenerate the post.');
        return;
    }

    navigator.clipboard.writeText(htmlContent).then(() => {
        alert('HTML code copied to clipboard!');
    }).catch(() => {
        // Fallback for older browsers
        const textarea = document.createElement('textarea');
        textarea.value = htmlContent;
        document.body.appendChild(textarea);
        textarea.select();
        document.execCommand('copy');
        document.body.removeChild(textarea);
        alert('HTML code copied to clipboard!');
    });
}

function generateSitemapEntry(slug) {
    const post = JSON.parse(localStorage.getItem(`post_meta_${slug}`) || '{}');
    const sitemapEntry = `
    <url>
        <loc>https://blog.littlestareducation.com/post/${slug}/</loc>
        <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
        <changefreq>monthly</changefreq>
        <priority>0.8</priority>
    </url>`;

    navigator.clipboard.writeText(sitemapEntry).then(() => {
        alert('Sitemap entry copied! Add this to your sitemap.xml file.');
    });
}

// Content templates for quick posting
function insertTemplate(templateType) {
    const templates = {
        'how-to': `
<h2>Introduction</h2>
<p>Brief introduction to the topic and why it's important for educators.</p>

<h2>Step-by-Step Guide</h2>
<h3>Step 1: [First Step]</h3>
<p>Detailed explanation of the first step.</p>

<h3>Step 2: [Second Step]</h3>
<p>Detailed explanation of the second step.</p>

<h3>Step 3: [Third Step]</h3>
<p>Detailed explanation of the third step.</p>

<h2>Tips for Success</h2>
<ul>
<li>Practical tip 1</li>
<li>Practical tip 2</li>
<li>Practical tip 3</li>
</ul>

<h2>Common Mistakes to Avoid</h2>
<p>List common pitfalls and how to avoid them.</p>

<h2>Conclusion</h2>
<p>Summarize the key points and encourage readers to try the techniques.</p>
        `,
        'resource-list': `
<h2>Why These Resources Are Essential</h2>
<p>Explain the importance of the resources you're sharing.</p>

<h2>Top [Number] [Resource Type] for [Grade Level/Subject]</h2>

<h3>1. [Resource Name]</h3>
<p>Description of the resource and why it's valuable.</p>
<ul>
<li>Key feature 1</li>
<li>Key feature 2</li>
<li>Best for: [specific use case]</li>
</ul>

<h3>2. [Resource Name]</h3>
<p>Description of the resource and why it's valuable.</p>
<ul>
<li>Key feature 1</li>
<li>Key feature 2</li>
<li>Best for: [specific use case]</li>
</ul>

<h2>How to Use These Resources Effectively</h2>
<p>Practical tips for implementation.</p>

<h2>Conclusion</h2>
<p>Encourage readers to try the resources and share their experiences.</p>
        `,
        'review': `
<h2>Overview</h2>
<p>Brief introduction to what you're reviewing and why it matters.</p>

<h2>Key Features</h2>
<ul>
<li>Feature 1: Description</li>
<li>Feature 2: Description</li>
<li>Feature 3: Description</li>
</ul>

<h2>Pros and Cons</h2>
<h3>Pros</h3>
<ul>
<li>Positive aspect 1</li>
<li>Positive aspect 2</li>
<li>Positive aspect 3</li>
</ul>

<h3>Cons</h3>
<ul>
<li>Limitation 1</li>
<li>Limitation 2</li>
</ul>

<h2>Who Should Use This</h2>
<p>Describe the ideal user or use case.</p>

<h2>Final Verdict</h2>
<p>Your overall recommendation and rating.</p>
        `
    };

    const template = templates[templateType];
    if (template && typeof tinymce !== 'undefined') {
        tinymce.get('post-content').setContent(template);
        alert(`${templateType} template inserted! Customize the content for your specific topic.`);
    }
}

// Quick SEO optimization
function optimizeSEO() {
    const title = document.getElementById('post-title').value;
    const excerpt = document.getElementById('post-excerpt').value;

    let suggestions = [];
    let score = 0;

    // Title optimization (25 points)
    if (title.length >= 30 && title.length <= 60) {
        score += 25;
    } else if (title.length >= 20) {
        score += 15;
        if (title.length < 30) suggestions.push('• Title could be longer. Aim for 30-60 characters.');
        if (title.length > 60) suggestions.push('• Title is too long. Keep it under 60 characters.');
    } else {
        suggestions.push('• Title is too short. Aim for 30-60 characters.');
    }

    // Meta description optimization (25 points)
    if (excerpt.length >= 120 && excerpt.length <= 160) {
        score += 25;
    } else if (excerpt.length >= 100) {
        score += 15;
        if (excerpt.length < 120) suggestions.push('• Meta description could be longer. Aim for 120-160 characters.');
        if (excerpt.length > 160) suggestions.push('• Meta description is too long. Keep it under 160 characters.');
    } else {
        suggestions.push('• Meta description needs work. Aim for 120-160 characters.');
    }

    // Keywords check (20 points)
    const keywords = document.getElementById('post-keywords').value;
    if (keywords && keywords.split(',').length >= 3) {
        score += 20;
    } else if (keywords) {
        score += 10;
        suggestions.push('• Add more relevant keywords (aim for 3-5).');
    } else {
        suggestions.push('• Add relevant keywords for better SEO.');
    }

    // Content check (30 points)
    const content = tinymce.get('post-content').getContent();
    const wordCount = content.replace(/<[^>]*>/g, '').split(/\s+/).length;
    if (wordCount >= 1000) {
        score += 30;
    } else if (wordCount >= 500) {
        score += 20;
        suggestions.push('• Consider adding more content. Aim for 1000+ words for better SEO.');
    } else if (wordCount >= 300) {
        score += 10;
        suggestions.push('• Content is too short. Aim for at least 500 words.');
    } else {
        suggestions.push('• Content is too short. Aim for at least 300 words minimum.');
    }

    // Internal linking check
    if (content.includes('littlestareducation.com')) {
        score += 5;
    } else {
        suggestions.push('• Add links to your main website for better conversion.');
    }

    // Show results
    let message = `SEO Score: ${score}/100\n\n`;
    if (score >= 80) {
        message += '✅ Excellent! Your post is well-optimized for SEO.';
    } else if (score >= 60) {
        message += '⚠️ Good, but could be improved:';
    } else {
        message += '❌ Needs improvement:';
    }

    if (suggestions.length > 0) {
        message += '\n\n' + suggestions.join('\n');
    }

    alert(message);
}

// Add CTA block to content
function addCTABlock() {
    const ctaHTML = `
<div class="post-cta">
    <h4>🎯 Need More Educational Resources?</h4>
    <p>Create custom worksheets, flashcards, and teaching materials with our free online tools.</p>
    <a href="https://littlestareducation.com/" target="_blank" class="btn btn-primary">Try Free Tools</a>
</div>
    `;

    if (typeof tinymce !== 'undefined') {
        const editor = tinymce.get('post-content');
        const currentContent = editor.getContent();
        editor.setContent(currentContent + ctaHTML);
        alert('CTA block added to your content!');
    }
}

// Add resource box to content
function addResourceBox() {
    const resourceHTML = `
<div class="resource-download">
    <h3>📚 Free Educational Resources</h3>
    <p>Get instant access to our library of worksheets, teaching guides, and classroom activities.</p>
    <ul>
        <li>✅ Printable worksheets for all grade levels</li>
        <li>✅ Interactive learning activities</li>
        <li>✅ Lesson plan templates</li>
        <li>✅ Assessment tools</li>
    </ul>
    <a href="https://littlestareducation.com/" target="_blank" class="btn btn-primary">Access Free Resources</a>
</div>
    `;

    if (typeof tinymce !== 'undefined') {
        const editor = tinymce.get('post-content');
        const currentContent = editor.getContent();
        editor.setContent(currentContent + resourceHTML);
        alert('Resource box added to your content!');
    }
}

// Preview post before publishing
function previewPost() {
    const title = document.getElementById('post-title').value;
    const excerpt = document.getElementById('post-excerpt').value;
    const content = tinymce.get('post-content').getContent();

    if (!title || !excerpt || !content) {
        alert('Please fill in the title, excerpt, and content before previewing.');
        return;
    }

    const previewWindow = window.open('', '_blank', 'width=800,height=600');
    previewWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>${title}</title>
            <style>
                body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
                .post-meta { color: #666; margin-bottom: 20px; }
                .post-title { color: #333; margin-bottom: 10px; }
                .post-excerpt { color: #666; font-style: italic; margin-bottom: 20px; }
                .post-content { line-height: 1.6; }
                .post-cta, .resource-download { background: #f0f8ff; padding: 20px; margin: 20px 0; border-radius: 8px; }
            </style>
        </head>
        <body>
            <div class="post-meta">Preview Mode</div>
            <h1 class="post-title">${title}</h1>
            <p class="post-excerpt">${excerpt}</p>
            <div class="post-content">${content}</div>
        </body>
        </html>
    `);
    previewWindow.document.close();
}

// Initialize admin panel when DOM is loaded
let adminPanelInstance;
document.addEventListener('DOMContentLoaded', () => {
    adminPanelInstance = new AdminPanel();
});

// Close modals when clicking outside
document.addEventListener('click', (e) => {
    if (e.target.classList.contains('modal')) {
        e.target.classList.remove('active');
        document.body.style.overflow = '';
    }
});
