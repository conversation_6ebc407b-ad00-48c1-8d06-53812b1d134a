/* CSS Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* CSS Variables for Consistent Design */
:root {
    /* Colors */
    --primary-color: #3498db;
    --primary-dark: #2980b9;
    --secondary-color: #e74c3c;
    --accent-color: #f39c12;
    --text-color: #2c3e50;
    --text-light: #7f8c8d;
    --background-color: #ffffff;
    --background-light: #f8f9fa;
    --border-color: #dee2e6;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --error-color: #e74c3c;
    
    /* Typography */
    --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-family-heading: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    --font-size-base: 16px;
    --font-size-sm: 14px;
    --font-size-lg: 18px;
    --font-size-xl: 20px;
    --font-size-2xl: 24px;
    --font-size-3xl: 32px;
    --font-size-4xl: 40px;
    --line-height-base: 1.6;
    --line-height-heading: 1.3;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
    
    /* Layout */
    --container-max-width: 1200px;
    --content-max-width: 800px;
    --sidebar-width: 300px;
    --border-radius: 8px;
    --border-radius-sm: 4px;
    --border-radius-lg: 12px;
    
    /* Shadows */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.1);
    
    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-base: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* Base Typography */
html {
    font-size: var(--font-size-base);
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family-primary);
    line-height: var(--line-height-base);
    color: var(--text-color);
    background-color: var(--background-color);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Headings */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-family-heading);
    line-height: var(--line-height-heading);
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    color: var(--text-color);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

/* Links */
a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover, a:focus {
    color: var(--primary-dark);
    text-decoration: underline;
}

/* Images */
img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* Container */
.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

/* Skip Link for Accessibility */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--text-color);
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: var(--border-radius-sm);
    z-index: 1000;
}

.skip-link:focus {
    top: 6px;
}

/* Header Styles */
.site-header {
    background: var(--background-color);
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: var(--shadow-sm);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) 0;
    gap: var(--spacing-lg);
}

/* Site Branding */
.site-branding {
    display: flex;
    align-items: center;
}

.logo-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    text-decoration: none;
}

.logo {
    width: 50px;
    height: 50px;
    border-radius: var(--border-radius);
}

.site-title h1 {
    font-size: var(--font-size-2xl);
    margin: 0;
    color: var(--text-color);
}

.tagline {
    font-size: var(--font-size-sm);
    color: var(--text-light);
    margin: 0;
}

/* Navigation */
.main-navigation {
    position: relative;
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--spacing-sm);
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background: var(--text-color);
    margin: 3px 0;
    transition: var(--transition-base);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: var(--spacing-lg);
    align-items: center;
}

.nav-link {
    font-weight: 500;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-fast);
    position: relative;
}

.nav-link:hover, .nav-link.active {
    background: var(--background-light);
    text-decoration: none;
}

.cta-link {
    background: var(--primary-color);
    color: white !important;
}

.cta-link:hover {
    background: var(--primary-dark);
}

/* Dropdown Menu */
.has-dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-base);
    z-index: 1000;
}

.has-dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu li {
    list-style: none;
}

.dropdown-menu a {
    display: block;
    padding: var(--spacing-sm) var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
}

.dropdown-menu a:hover {
    background: var(--background-light);
}

/* Search */
.header-search {
    position: relative;
}

.search-form {
    display: flex;
    align-items: center;
    background: var(--background-light);
    border-radius: var(--border-radius);
    padding: var(--spacing-xs);
}

.search-input {
    border: none;
    background: none;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
    width: 250px;
    outline: none;
}

.search-button {
    background: none;
    border: none;
    padding: var(--spacing-sm);
    cursor: pointer;
    color: var(--text-light);
    transition: color var(--transition-fast);
}

.search-button:hover {
    color: var(--primary-color);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--border-radius);
    font-size: var(--font-size-base);
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    gap: var(--spacing-sm);
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-dark);
    color: white;
    text-decoration: none;
}

.btn-secondary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: white;
    text-decoration: none;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    padding: var(--spacing-3xl) 0;
    text-align: center;
}

.hero-title {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--spacing-lg);
    color: white;
}

.hero-description {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-xl);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    opacity: 0.9;
}

.hero-cta {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    flex-wrap: wrap;
}

.hero-cta .btn-secondary {
    border-color: white;
    color: white;
}

.hero-cta .btn-secondary:hover {
    background: white;
    color: var(--primary-color);
}

/* Section Styles */
.featured-posts, .latest-posts {
    padding: var(--spacing-3xl) 0;
}

.section-title {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: var(--primary-color);
    border-radius: 2px;
}

/* Content Layout */
.content-layout {
    display: grid;
    grid-template-columns: 1fr var(--sidebar-width);
    gap: var(--spacing-2xl);
    align-items: start;
}

/* Posts Grid */
.posts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

/* Post Card */
.post-card {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: transform var(--transition-base), box-shadow var(--transition-base);
}

.post-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.post-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.post-content {
    padding: var(--spacing-lg);
}

.post-meta {
    display: flex;
    gap: var(--spacing-md);
    font-size: var(--font-size-sm);
    color: var(--text-light);
    margin-bottom: var(--spacing-sm);
}

.post-title {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-sm);
}

.post-title a {
    color: var(--text-color);
    text-decoration: none;
}

.post-title a:hover {
    color: var(--primary-color);
}

.post-excerpt {
    color: var(--text-light);
    margin-bottom: var(--spacing-md);
}

.read-more {
    font-weight: 500;
    color: var(--primary-color);
}

/* Sidebar Widgets */
.widget {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    box-shadow: var(--shadow-sm);
}

.widget-title {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-lg);
    color: var(--text-color);
}

/* Newsletter Widget */
.newsletter-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.newsletter-input {
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: var(--font-size-base);
}

/* Categories Widget */
.categories-list {
    list-style: none;
}

.categories-list li {
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-sm) 0;
}

.categories-list li:last-child {
    border-bottom: none;
}

/* CTA Widget */
.cta-widget {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
}

.cta-widget .widget-title {
    color: white;
}

.cta-widget p {
    opacity: 0.9;
}

.cta-widget .btn-primary {
    background: white;
    color: var(--primary-color);
}

.cta-widget .btn-primary:hover {
    background: var(--background-light);
}

/* Footer */
.site-footer {
    background: var(--text-color);
    color: white;
    padding: var(--spacing-3xl) 0 var(--spacing-xl) 0;
}

.footer-widgets {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-2xl);
    margin-bottom: var(--spacing-2xl);
}

.footer-widget h4 {
    color: white;
    margin-bottom: var(--spacing-lg);
}

.footer-widget ul {
    list-style: none;
}

.footer-widget li {
    margin-bottom: var(--spacing-sm);
}

.footer-widget a {
    color: rgba(255, 255, 255, 0.8);
    transition: color var(--transition-fast);
}

.footer-widget a:hover {
    color: white;
    text-decoration: none;
}

.social-links {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.social-links a {
    font-size: var(--font-size-xl);
    text-decoration: none;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding-top: var(--spacing-xl);
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 var(--spacing-sm);
    }
    
    .header-content {
        flex-wrap: wrap;
        gap: var(--spacing-md);
    }
    
    .mobile-menu-toggle {
        display: flex;
    }
    
    .nav-menu {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        flex-direction: column;
        padding: var(--spacing-md);
        box-shadow: var(--shadow-lg);
    }
    
    .nav-menu.active {
        display: flex;
    }
    
    .search-input {
        width: 200px;
    }
    
    .hero-title {
        font-size: var(--font-size-3xl);
    }
    
    .hero-cta {
        flex-direction: column;
        align-items: center;
    }
    
    .content-layout {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }
    
    .posts-grid {
        grid-template-columns: 1fr;
    }
    
    .footer-widgets {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }
}

@media (max-width: 480px) {
    .hero-section {
        padding: var(--spacing-2xl) 0;
    }
    
    .hero-title {
        font-size: var(--font-size-2xl);
    }
    
    .hero-description {
        font-size: var(--font-size-base);
    }
    
    .section-title {
        font-size: var(--font-size-2xl);
    }
    
    .post-content {
        padding: var(--spacing-md);
    }
    
    .widget {
        padding: var(--spacing-lg);
    }
}

/* Performance Optimizations */
.post-image {
    loading: lazy;
}

/* Print Styles */
@media print {
    .site-header,
    .hero-section,
    .sidebar,
    .site-footer {
        display: none;
    }
    
    .main-content {
        margin: 0;
        padding: 0;
    }
    
    .post-card {
        box-shadow: none;
        border: 1px solid var(--border-color);
        break-inside: avoid;
    }
}

/* Traffic Conversion Features */
.conversion-banner {
    background: linear-gradient(135deg, var(--accent-color), #e67e22);
    color: white;
    padding: var(--spacing-xl);
    text-align: center;
    margin: var(--spacing-2xl) 0;
    border-radius: var(--border-radius-lg);
}

.conversion-banner h3 {
    color: white;
    margin-bottom: var(--spacing-md);
}

.conversion-banner p {
    opacity: 0.9;
    margin-bottom: var(--spacing-lg);
}

.conversion-banner .btn {
    background: white;
    color: var(--accent-color);
    font-weight: 600;
}

.conversion-banner .btn:hover {
    background: var(--background-light);
    transform: translateY(-2px);
}

/* Strategic CTAs */
.post-cta {
    background: var(--background-light);
    border: 2px solid var(--primary-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    margin: var(--spacing-xl) 0;
    text-align: center;
}

.post-cta h4 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

.post-cta p {
    margin-bottom: var(--spacing-md);
    color: var(--text-light);
}

.inline-cta {
    background: #fff3cd;
    border-left: 4px solid var(--accent-color);
    padding: var(--spacing-md);
    margin: var(--spacing-lg) 0;
    border-radius: 0 var(--border-radius-sm) var(--border-radius-sm) 0;
}

.inline-cta strong {
    color: var(--accent-color);
}

/* Newsletter Popup */
.newsletter-popup {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-xl);
    max-width: 350px;
    z-index: 1000;
    transform: translateY(100px);
    opacity: 0;
    transition: all var(--transition-base);
}

.newsletter-popup.show {
    transform: translateY(0);
    opacity: 1;
}

.newsletter-popup .close-popup {
    position: absolute;
    top: 10px;
    right: 15px;
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: var(--text-light);
}

.newsletter-popup h4 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

.newsletter-popup p {
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-md);
    color: var(--text-light);
}

/* Exit Intent Modal */
.exit-intent-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 2000;
    display: none;
    align-items: center;
    justify-content: center;
}

.exit-intent-modal.show {
    display: flex;
}

.exit-intent-content {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-2xl);
    max-width: 500px;
    text-align: center;
    position: relative;
}

.exit-intent-content .close-modal {
    position: absolute;
    top: 15px;
    right: 20px;
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-light);
}

.exit-intent-content h3 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.exit-intent-content .highlight {
    background: var(--accent-color);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
}

/* Social Proof */
.social-proof {
    background: var(--success-color);
    color: white;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    margin: var(--spacing-md) 0;
    text-align: center;
}

.social-proof .count {
    font-weight: 700;
    font-size: var(--font-size-base);
}

/* Resource Download CTA */
.resource-download {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    margin: var(--spacing-2xl) 0;
    text-align: center;
}

.resource-download h3 {
    color: white;
    margin-bottom: var(--spacing-md);
}

.resource-download .btn {
    background: white;
    color: var(--primary-color);
    font-weight: 600;
    font-size: var(--font-size-lg);
    padding: var(--spacing-md) var(--spacing-xl);
}

.resource-download .btn:hover {
    background: var(--background-light);
    transform: translateY(-2px);
}

/* Sticky CTA Bar */
.sticky-cta {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--primary-color);
    color: white;
    padding: var(--spacing-md);
    text-align: center;
    z-index: 100;
    transform: translateY(100%);
    transition: transform var(--transition-base);
}

.sticky-cta.show {
    transform: translateY(0);
}

.sticky-cta .btn {
    background: white;
    color: var(--primary-color);
    margin-left: var(--spacing-md);
}

.sticky-cta .close-sticky {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
}

/* Related Posts with CTAs */
.related-posts {
    background: var(--background-light);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    margin: var(--spacing-2xl) 0;
}

.related-posts h3 {
    text-align: center;
    margin-bottom: var(--spacing-xl);
    color: var(--text-color);
}

.related-posts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.related-post-card {
    background: white;
    padding: var(--spacing-lg);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    text-align: center;
}

.related-post-card h4 {
    font-size: var(--font-size-base);
    margin-bottom: var(--spacing-sm);
}

.related-post-card p {
    font-size: var(--font-size-sm);
    color: var(--text-light);
    margin-bottom: var(--spacing-md);
}

/* Focus Styles for Accessibility */
button:focus,
input:focus,
textarea:focus,
select:focus,
a:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner {
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
