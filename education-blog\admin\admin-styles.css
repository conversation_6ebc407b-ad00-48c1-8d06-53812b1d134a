/* Admin Panel Styles */
.admin-body {
    background: #f8f9fa;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Admin Header */
.admin-header {
    background: #2c3e50;
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.admin-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 1rem;
}

.admin-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.admin-header h1 {
    margin: 0;
    font-size: 1.5rem;
    color: white;
}

.admin-nav {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.admin-nav-link {
    padding: 0.5rem 1rem;
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    border-radius: 4px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.admin-nav-link:hover,
.admin-nav-link.active {
    background: rgba(255,255,255,0.1);
    color: white;
}

.admin-nav-link.view-site {
    background: #3498db;
    color: white;
}

.admin-nav-link.view-site:hover {
    background: #2980b9;
}

/* Admin Main */
.admin-main {
    padding: 2rem 0;
    min-height: calc(100vh - 80px);
}

.admin-section {
    display: none;
}

.admin-section.active {
    display: block;
}

.admin-section-header {
    margin-bottom: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.admin-section-header h2 {
    margin: 0;
    color: #2c3e50;
}

.admin-section-header p {
    margin: 0.5rem 0 0 0;
    color: #7f8c8d;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-icon {
    font-size: 2rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    border-radius: 50%;
}

.stat-content h3 {
    margin: 0;
    font-size: 2rem;
    color: #2c3e50;
}

.stat-content p {
    margin: 0;
    color: #7f8c8d;
    font-size: 0.9rem;
}

/* Quick Actions */
.quick-actions {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.quick-actions h3 {
    margin: 0 0 1rem 0;
    color: #2c3e50;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Recent Activity */
.recent-activity {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.recent-activity h3 {
    margin: 0 0 1rem 0;
    color: #2c3e50;
}

.activity-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.activity-item {
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 4px;
    border-left: 4px solid #3498db;
}

/* Posts Management */
.posts-filter {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.search-input,
.filter-select {
    padding: 0.5rem;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 0.9rem;
}

.search-input {
    flex: 1;
    min-width: 200px;
}

.filter-select {
    min-width: 150px;
}

.posts-table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

.posts-table {
    width: 100%;
    border-collapse: collapse;
}

.posts-table th,
.posts-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

.posts-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
}

.posts-table tr:hover {
    background: #f8f9fa;
}

.status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-published {
    background: #d4edda;
    color: #155724;
}

.status-draft {
    background: #fff3cd;
    color: #856404;
}

.status-scheduled {
    background: #d1ecf1;
    color: #0c5460;
}

.action-btn {
    padding: 0.25rem 0.5rem;
    margin: 0 0.25rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    text-decoration: none;
    display: inline-block;
}

.action-btn.edit {
    background: #3498db;
    color: white;
}

.action-btn.delete {
    background: #e74c3c;
    color: white;
}

.action-btn.view {
    background: #27ae60;
    color: white;
}

/* Categories Grid */
.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.category-card {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.category-card h3 {
    margin: 0 0 0.5rem 0;
    color: #2c3e50;
}

.category-stats {
    display: flex;
    justify-content: space-between;
    margin: 1rem 0;
    font-size: 0.9rem;
    color: #7f8c8d;
}

/* SEO Settings */
.seo-settings {
    display: grid;
    gap: 2rem;
}

.seo-card {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.seo-card h3 {
    margin: 0 0 1rem 0;
    color: #2c3e50;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #2c3e50;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 0.9rem;
}

.form-group small {
    color: #7f8c8d;
    font-size: 0.8rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.seo-tools {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.seo-checklist {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.checklist-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 4px;
}

.checklist-item.completed {
    background: #d4edda;
}

.checklist-item.warning {
    background: #fff3cd;
}

.checklist-item.error {
    background: #f8d7da;
}

/* Analytics */
.analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.analytics-card {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.analytics-card h3 {
    margin: 0 0 1rem 0;
    color: #2c3e50;
}

.analytics-integration {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    overflow-y: auto;
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
}

.modal-content {
    background: white;
    border-radius: 8px;
    max-width: 800px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #dee2e6;
}

.modal-header h3 {
    margin: 0;
    color: #2c3e50;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #7f8c8d;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    padding: 1.5rem;
    border-top: 1px solid #dee2e6;
}

/* SEO Preview */
.seo-preview {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 4px;
    margin: 1rem 0;
}

.seo-preview h4 {
    margin: 0 0 0.5rem 0;
    color: #2c3e50;
}

.seo-preview-content {
    font-family: arial, sans-serif;
}

.seo-title {
    color: #1a0dab;
    font-size: 18px;
    line-height: 1.3;
    margin-bottom: 2px;
}

.seo-url {
    color: #006621;
    font-size: 14px;
    margin-bottom: 2px;
}

.seo-description {
    color: #545454;
    font-size: 13px;
    line-height: 1.4;
}

/* Internal Linking */
.internal-linking {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 4px;
    margin: 1rem 0;
}

.internal-linking h4 {
    margin: 0 0 0.5rem 0;
    color: #2c3e50;
}

.linking-suggestion {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    background: white;
    border-radius: 4px;
    margin-bottom: 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-header-content {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .admin-nav {
        width: 100%;
        justify-content: flex-start;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .posts-filter {
        flex-direction: column;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .seo-tools {
        flex-direction: column;
    }
    
    .analytics-grid {
        grid-template-columns: 1fr;
    }
    
    .posts-table-container {
        overflow-x: auto;
    }
    
    .modal-content {
        margin: 1rem;
        max-height: calc(100vh - 2rem);
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner {
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
    display: inline-block;
    margin-right: 0.5rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
