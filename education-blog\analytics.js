// Advanced Analytics and Tracking System
class AdvancedAnalytics {
    constructor() {
        this.sessionData = {
            startTime: Date.now(),
            pageViews: 0,
            interactions: 0,
            scrollDepth: 0,
            timeOnPage: 0
        };
        this.init();
    }

    init() {
        this.setupGoogleAnalytics();
        this.setupSearchConsole();
        this.setupHeatmapTracking();
        this.setupPerformanceMonitoring();
        this.setupSocialSharing();
        this.setupConversionTracking();
        this.setupUserBehaviorTracking();
    }

    // Google Analytics 4 Setup
    setupGoogleAnalytics() {
        // Enhanced ecommerce and event tracking
        if (typeof gtag !== 'undefined') {
            // Track page view with custom parameters
            gtag('config', 'GA_MEASUREMENT_ID', {
                custom_map: {
                    'custom_parameter_1': 'blog_category',
                    'custom_parameter_2': 'content_type'
                },
                page_title: document.title,
                page_location: window.location.href,
                content_group1: this.getContentCategory(),
                content_group2: 'blog_post'
            });

            // Track user engagement
            this.trackEngagementMetrics();
        }
    }

    // Search Console Integration
    setupSearchConsole() {
        // Add Search Console verification meta tag dynamically
        const verification = document.createElement('meta');
        verification.name = 'google-site-verification';
        verification.content = 'YOUR_SEARCH_CONSOLE_VERIFICATION_CODE';
        document.head.appendChild(verification);

        // Track search queries and impressions
        this.trackSearchPerformance();
    }

    // Heatmap and User Behavior Tracking
    setupHeatmapTracking() {
        // Track click patterns
        document.addEventListener('click', (e) => {
            const element = e.target;
            const elementInfo = {
                tag: element.tagName,
                class: element.className,
                id: element.id,
                text: element.textContent?.substring(0, 50),
                position: {
                    x: e.clientX,
                    y: e.clientY
                }
            };

            this.trackEvent('click_heatmap', {
                element_info: JSON.stringify(elementInfo),
                page_url: window.location.pathname
            });
        });

        // Track scroll heatmap
        this.setupScrollHeatmap();
    }

    // Performance Monitoring
    setupPerformanceMonitoring() {
        // Core Web Vitals tracking
        this.trackCoreWebVitals();
        
        // Page load performance
        window.addEventListener('load', () => {
            const perfData = performance.getEntriesByType('navigation')[0];
            
            if (perfData) {
                this.trackEvent('page_performance', {
                    load_time: Math.round(perfData.loadEventEnd - perfData.fetchStart),
                    dom_content_loaded: Math.round(perfData.domContentLoadedEventEnd - perfData.fetchStart),
                    first_byte: Math.round(perfData.responseStart - perfData.fetchStart),
                    page_size: this.getPageSize()
                });
            }
        });

        // Track JavaScript errors
        window.addEventListener('error', (e) => {
            this.trackEvent('javascript_error', {
                error_message: e.message,
                error_source: e.filename,
                error_line: e.lineno,
                error_column: e.colno,
                page_url: window.location.pathname
            });
        });
    }

    // Social Sharing Tracking
    setupSocialSharing() {
        // Add social sharing buttons with tracking
        this.addSocialSharingButtons();
        
        // Track social media referrals
        this.trackSocialReferrals();
    }

    // Conversion Tracking
    setupConversionTracking() {
        // Track newsletter signups
        document.addEventListener('submit', (e) => {
            if (e.target.classList.contains('newsletter-form')) {
                this.trackConversion('newsletter_signup', {
                    form_location: this.getFormLocation(e.target),
                    user_type: this.getUserType()
                });
            }
        });

        // Track main site clicks
        document.querySelectorAll('a[href*="littlestareducation.com"]').forEach(link => {
            link.addEventListener('click', () => {
                this.trackConversion('main_site_click', {
                    link_text: link.textContent.trim(),
                    link_position: this.getLinkPosition(link),
                    page_section: this.getPageSection(link)
                });
            });
        });

        // Track resource downloads
        document.querySelectorAll('a[href*=".pdf"], a[href*="download"]').forEach(link => {
            link.addEventListener('click', () => {
                this.trackConversion('resource_download', {
                    resource_name: link.textContent.trim(),
                    resource_type: this.getResourceType(link.href)
                });
            });
        });
    }

    // User Behavior Tracking
    setupUserBehaviorTracking() {
        // Track time on page
        this.startTimeTracking();
        
        // Track scroll depth
        this.trackScrollDepth();
        
        // Track reading progress
        this.trackReadingProgress();
        
        // Track user interactions
        this.trackUserInteractions();
    }

    // Core Web Vitals
    trackCoreWebVitals() {
        // Largest Contentful Paint (LCP)
        new PerformanceObserver((entryList) => {
            const entries = entryList.getEntries();
            const lastEntry = entries[entries.length - 1];
            
            this.trackEvent('core_web_vitals', {
                metric: 'LCP',
                value: Math.round(lastEntry.startTime),
                rating: this.getLCPRating(lastEntry.startTime)
            });
        }).observe({ entryTypes: ['largest-contentful-paint'] });

        // First Input Delay (FID)
        new PerformanceObserver((entryList) => {
            const firstInput = entryList.getEntries()[0];
            
            this.trackEvent('core_web_vitals', {
                metric: 'FID',
                value: Math.round(firstInput.processingStart - firstInput.startTime),
                rating: this.getFIDRating(firstInput.processingStart - firstInput.startTime)
            });
        }).observe({ entryTypes: ['first-input'] });

        // Cumulative Layout Shift (CLS)
        let clsValue = 0;
        new PerformanceObserver((entryList) => {
            for (const entry of entryList.getEntries()) {
                if (!entry.hadRecentInput) {
                    clsValue += entry.value;
                }
            }
            
            this.trackEvent('core_web_vitals', {
                metric: 'CLS',
                value: Math.round(clsValue * 1000) / 1000,
                rating: this.getCLSRating(clsValue)
            });
        }).observe({ entryTypes: ['layout-shift'] });
    }

    // Scroll Heatmap
    setupScrollHeatmap() {
        let scrollData = [];
        
        window.addEventListener('scroll', () => {
            const scrollPercent = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);
            const timestamp = Date.now();
            
            scrollData.push({
                percent: scrollPercent,
                timestamp: timestamp,
                viewport: {
                    width: window.innerWidth,
                    height: window.innerHeight
                }
            });
            
            // Send data every 10 scroll events
            if (scrollData.length >= 10) {
                this.trackEvent('scroll_heatmap', {
                    scroll_data: JSON.stringify(scrollData),
                    page_url: window.location.pathname
                });
                scrollData = [];
            }
        });
    }

    // Time Tracking
    startTimeTracking() {
        let startTime = Date.now();
        let isActive = true;
        
        // Track when user becomes inactive
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                isActive = false;
                this.sessionData.timeOnPage += Date.now() - startTime;
            } else {
                isActive = true;
                startTime = Date.now();
            }
        });

        // Send time data before page unload
        window.addEventListener('beforeunload', () => {
            if (isActive) {
                this.sessionData.timeOnPage += Date.now() - startTime;
            }
            
            this.trackEvent('session_end', {
                total_time: this.sessionData.timeOnPage,
                page_views: this.sessionData.pageViews,
                interactions: this.sessionData.interactions,
                scroll_depth: this.sessionData.scrollDepth
            });
        });
    }

    // Reading Progress
    trackReadingProgress() {
        const article = document.querySelector('article, .post-content');
        if (!article) return;

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const progress = Math.round((entry.intersectionRatio) * 100);
                    
                    this.trackEvent('reading_progress', {
                        progress_percent: progress,
                        article_length: article.textContent.length,
                        estimated_read_time: Math.ceil(article.textContent.length / 200) // 200 words per minute
                    });
                }
            });
        }, { threshold: [0.25, 0.5, 0.75, 1.0] });

        observer.observe(article);
    }

    // Social Sharing Buttons
    addSocialSharingButtons() {
        const shareContainer = document.createElement('div');
        shareContainer.className = 'social-sharing';
        shareContainer.innerHTML = `
            <h4>Share this article:</h4>
            <div class="share-buttons">
                <button class="share-btn facebook" data-platform="facebook">📘 Facebook</button>
                <button class="share-btn twitter" data-platform="twitter">🐦 Twitter</button>
                <button class="share-btn linkedin" data-platform="linkedin">💼 LinkedIn</button>
                <button class="share-btn pinterest" data-platform="pinterest">📌 Pinterest</button>
                <button class="share-btn email" data-platform="email">📧 Email</button>
            </div>
        `;

        // Insert after article content
        const article = document.querySelector('article, .post-content');
        if (article) {
            article.appendChild(shareContainer);
        }

        // Add click tracking
        shareContainer.addEventListener('click', (e) => {
            if (e.target.classList.contains('share-btn')) {
                const platform = e.target.dataset.platform;
                this.trackSocialShare(platform);
                this.openShareWindow(platform);
            }
        });
    }

    // Track Social Share
    trackSocialShare(platform) {
        this.trackEvent('social_share', {
            platform: platform,
            page_title: document.title,
            page_url: window.location.href,
            content_type: this.getContentCategory()
        });
    }

    // Open Share Window
    openShareWindow(platform) {
        const url = encodeURIComponent(window.location.href);
        const title = encodeURIComponent(document.title);
        const description = encodeURIComponent(document.querySelector('meta[name="description"]')?.content || '');
        
        let shareUrl = '';
        
        switch (platform) {
            case 'facebook':
                shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${url}`;
                break;
            case 'twitter':
                shareUrl = `https://twitter.com/intent/tweet?url=${url}&text=${title}`;
                break;
            case 'linkedin':
                shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${url}`;
                break;
            case 'pinterest':
                const image = encodeURIComponent(document.querySelector('meta[property="og:image"]')?.content || '');
                shareUrl = `https://pinterest.com/pin/create/button/?url=${url}&media=${image}&description=${description}`;
                break;
            case 'email':
                shareUrl = `mailto:?subject=${title}&body=${description}%0A%0A${url}`;
                break;
        }
        
        if (shareUrl && platform !== 'email') {
            window.open(shareUrl, '_blank', 'width=600,height=400');
        } else if (platform === 'email') {
            window.location.href = shareUrl;
        }
    }

    // Utility Functions
    trackEvent(eventName, parameters = {}) {
        if (typeof gtag !== 'undefined') {
            gtag('event', eventName, {
                ...parameters,
                timestamp: Date.now(),
                user_agent: navigator.userAgent,
                screen_resolution: `${screen.width}x${screen.height}`,
                viewport_size: `${window.innerWidth}x${window.innerHeight}`
            });
        }
    }

    trackConversion(conversionType, parameters = {}) {
        this.trackEvent('conversion', {
            conversion_type: conversionType,
            ...parameters
        });
    }

    getContentCategory() {
        const breadcrumb = document.querySelector('.breadcrumb-list');
        if (breadcrumb) {
            const categoryLink = breadcrumb.querySelector('li:nth-child(2) a');
            return categoryLink ? categoryLink.textContent : 'uncategorized';
        }
        return 'uncategorized';
    }

    getUserType() {
        // Determine if user is new or returning based on localStorage
        const isReturning = localStorage.getItem('user_visited');
        if (!isReturning) {
            localStorage.setItem('user_visited', 'true');
            return 'new';
        }
        return 'returning';
    }

    getFormLocation(form) {
        if (form.closest('.sidebar')) return 'sidebar';
        if (form.closest('.hero-section')) return 'hero';
        if (form.closest('.footer')) return 'footer';
        return 'content';
    }

    getLinkPosition(link) {
        const rect = link.getBoundingClientRect();
        return {
            x: Math.round(rect.left),
            y: Math.round(rect.top),
            visible: rect.top >= 0 && rect.bottom <= window.innerHeight
        };
    }

    getPageSection(element) {
        if (element.closest('header')) return 'header';
        if (element.closest('.sidebar')) return 'sidebar';
        if (element.closest('footer')) return 'footer';
        if (element.closest('article')) return 'article';
        return 'main';
    }

    getResourceType(url) {
        if (url.includes('.pdf')) return 'pdf';
        if (url.includes('.doc')) return 'document';
        if (url.includes('.zip')) return 'archive';
        return 'link';
    }

    getPageSize() {
        return document.documentElement.outerHTML.length;
    }

    // Core Web Vitals Rating Functions
    getLCPRating(value) {
        if (value <= 2500) return 'good';
        if (value <= 4000) return 'needs_improvement';
        return 'poor';
    }

    getFIDRating(value) {
        if (value <= 100) return 'good';
        if (value <= 300) return 'needs_improvement';
        return 'poor';
    }

    getCLSRating(value) {
        if (value <= 0.1) return 'good';
        if (value <= 0.25) return 'needs_improvement';
        return 'poor';
    }

    trackScrollDepth() {
        let maxScroll = 0;
        
        window.addEventListener('scroll', () => {
            const scrollPercent = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);
            
            if (scrollPercent > maxScroll) {
                maxScroll = scrollPercent;
                this.sessionData.scrollDepth = maxScroll;
                
                // Track milestone scrolls
                if ([25, 50, 75, 90, 100].includes(scrollPercent)) {
                    this.trackEvent('scroll_milestone', {
                        depth_percent: scrollPercent
                    });
                }
            }
        });
    }

    trackUserInteractions() {
        ['click', 'keydown', 'scroll', 'mousemove'].forEach(eventType => {
            document.addEventListener(eventType, () => {
                this.sessionData.interactions++;
            }, { passive: true });
        });
    }

    trackSearchPerformance() {
        // Track internal search if implemented
        const searchForms = document.querySelectorAll('.search-form');
        searchForms.forEach(form => {
            form.addEventListener('submit', (e) => {
                const query = form.querySelector('input[type="search"]').value;
                this.trackEvent('internal_search', {
                    search_query: query,
                    results_count: 0 // Would be populated by search results
                });
            });
        });
    }

    trackSocialReferrals() {
        const referrer = document.referrer;
        const socialPlatforms = ['facebook.com', 'twitter.com', 'linkedin.com', 'pinterest.com', 'instagram.com'];
        
        const socialReferrer = socialPlatforms.find(platform => referrer.includes(platform));
        if (socialReferrer) {
            this.trackEvent('social_referral', {
                platform: socialReferrer,
                referrer_url: referrer
            });
        }
    }
}

// Initialize Advanced Analytics
document.addEventListener('DOMContentLoaded', () => {
    new AdvancedAnalytics();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AdvancedAnalytics;
}
